// Global filter state
const filters = {
    mos: [],
    hks: []
};

// Load all available MOs and HKs
// Load all available MOs and HKs with their rail types
async function loadAvailableItems() {
    try {
        // Show loading indicator
        document.getElementById('mo-filters').innerHTML = '<div class="text-gray-500">Loading...</div>';
        document.getElementById('hk-filters').innerHTML = '<div class="text-gray-500">Loading...</div>';
        
        const response = await fetch('/auto-cut/available-items');
        const data = await response.json();
        
        if (data.status === 'success') {
            // Initialize filters with rail type information
            filters.mos = data.mos || [];
            filters.hks = data.hks || [];
            
            // Add a map to store MO to rail type mapping
            filters.moTypes = {};
            
            // Fetch rail type for each MO
            for (const mo of filters.mos) {
                try {
                    const typeResponse = await fetch(`/auto-cut/mo-details/${mo}`);
                    const typeData = await typeResponse.json();
                    if (typeData && typeData.rail_type) {
                        filters.moTypes[mo] = typeData.rail_type;
                    }
                } catch (e) {
                    console.warn(`Could not fetch rail type for ${mo}, using default`);
                    // Set a default rail type based on the arrangement results
                    if (!filters.moTypes[mo]) {
                        filters.moTypes[mo] = "Unknown";
                    }
                }
            }
            
            renderFilters();
            
            // Generate arrangement automatically if items were found
            if ((filters.mos.length > 0 || filters.hks.length > 0)) {
                fetchArrangement();
            }
        } else {
            throw new Error(data.message || 'Failed to load items');
        }
    } catch (error) {
        console.error('Error loading available items:', error);
        document.getElementById('mo-filters').innerHTML = 
            `<div class="text-red-500">Error loading MOs: ${error.message}</div>`;
        document.getElementById('hk-filters').innerHTML = 
            `<div class="text-red-500">Error loading HKs: ${error.message}</div>`;
    }
}

// Add MO filter
function addMoFilter() {
    const input = document.getElementById('mo-input');
    const value = input.value.trim();
    
    if (value && !filters.mos.includes(value)) {
        filters.mos.push(value);
        renderFilters();
        input.value = '';
    }
}

// Add HK filter
function addHkFilter() {
    const input = document.getElementById('hk-input');
    const value = input.value.trim();
    
    if (value && !filters.hks.includes(value)) {
        filters.hks.push(value);
        renderFilters();
        input.value = '';
    }
}

// Remove filter
function removeFilter(type, value) {
    if (type === 'mo') {
        filters.mos = filters.mos.filter(mo => mo !== value);
    } else {
        filters.hks = filters.hks.filter(hk => hk !== value);
    }
    renderFilters();
}

// Clear all filters
function clearFilters() {
    filters.mos = [];
    filters.hks = [];
    renderFilters();
}

// Restore all original filters
async function restoreAllFilters() {
    try {
        const response = await fetch('/auto-cut/available-items');
        const data = await response.json();
        
        if (data.status === 'success') {
            filters.mos = data.mos || [];
            filters.hks = data.hks || [];
            renderFilters();
        } else {
            throw new Error(data.message || 'Failed to load items');
        }
    } catch (error) {
        console.error('Error restoring filters:', error);
        alert('Failed to restore all filters: ' + error.message);
    }
}

// Render filters UI
function renderFilters() {
    const moContainer = document.getElementById('mo-filters');
    const hkContainer = document.getElementById('hk-filters');
    
    // Render MO filters
    moContainer.innerHTML = '';
    if (filters.mos.length === 0) {
        moContainer.innerHTML = '<div class="text-gray-400 text-sm">No MO filters added</div>';
    } else {
        filters.mos.forEach(mo => {
            const token = document.createElement('span');
            token.className = 'token';
            
            // Include rail type if available
            const railType = filters.moTypes && filters.moTypes[mo] ? 
                `<small class="mo-type">${filters.moTypes[mo]}</small>` : '';
            
            token.innerHTML = `
                <span>${mo}</span>${railType}
                <span class="token-delete" onclick="removeFilter('mo', '${mo}')">&times;</span>
            `;
            moContainer.appendChild(token);
        });
    }
    
    // Render HK filters (unchanged)
    hkContainer.innerHTML = '';
    if (filters.hks.length === 0) {
        hkContainer.innerHTML = '<div class="text-gray-400 text-sm">No HK filters added</div>';
    } else {
        filters.hks.forEach(hk => {
            const token = document.createElement('span');
            token.className = 'token';
            token.innerHTML = `
                <span>${hk}</span>
                <span class="token-delete" onclick="removeFilter('hk', '${hk}')">&times;</span>
            `;
            hkContainer.appendChild(token);
        });
    }
}

// Add enter key handler to inputs
document.getElementById('mo-input').addEventListener('keyup', function(event) {
    if (event.key === 'Enter') {
        addMoFilter();
    }
});

document.getElementById('hk-input').addEventListener('keyup', function(event) {
    if (event.key === 'Enter') {
        addHkFilter();
    }
});

async function fetchArrangement() {
    try {
        // Show loading state
        document.getElementById('arrangement-container').innerHTML = `
            <div class="col-span-1 text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-2"></div>
                <div>Generating optimal rail arrangements...</div>
            </div>
        `;
        
        console.log("Fetching arrangement data with filters:", filters);
        
        // Prepare request with filters - now only sending MOs, no HKs needed
        const requestBody = {};
        
        if (filters.mos.length > 0) {
            requestBody.specific_mos = filters.mos;
        }
        
        console.log("Sending request with body:", requestBody);
        
        const response = await fetch('/auto-cut/arrangement-auto', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        console.log("Received data:", data);

        if (!response.ok) {
            throw new Error(data.message || 'Failed to fetch arrangement data');
        }

        if (data.status === 'success' && data.details && data.details.results) {
            if (data.details.results.length === 0) {
                document.getElementById('arrangement-container').innerHTML = `
                    <div class="col-span-1 text-center text-gray-500 py-10">
                        No arrangements found for the selected filters.
                        <br><br>
                        <div class="mt-4">
                            <button onclick="restoreAllFilters()" class="bg-blue-500 text-white px-4 py-2 rounded mr-2">
                                Restore All Filters
                            </button>
                            <button onclick="fetchArrangement()" class="bg-green-500 text-white px-4 py-2 rounded">
                                Try Again
                            </button>
                        </div>
                    </div>
                `;
            } else {
                displayArrangement(data.details.results);
            }
        } else {
            throw new Error('Invalid data format received from server');
        }
    } catch (error) {
        console.error('Error:', error);
        document.getElementById('arrangement-container').innerHTML = 
            `<div class="col-span-1 text-red-500 p-4 bg-red-50 rounded">
                Error loading arrangement data: ${error.message}
                <br>
                <button onclick="fetchArrangement()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">
                    Retry
                </button>
            </div>`;
    }
}

function displayArrangement(results) {
    console.log("Displaying arrangement results:", results);
    const container = document.getElementById('arrangement-container');
    container.innerHTML = '';
    
    // Use actual lengths from backend instead of estimating
    function getMoLengths(rail) {
        const moLengthMap = {};
        
        for (let i = 0; i < rail.mos.length; i++) {
            const mo = rail.mos[i];
            // Use the actual length from backend instead of calculating
            const length = rail.cut_lengths[i];
            
            // Store the actual length
            moLengthMap[mo] = length;
        }
        
        return moLengthMap;
    }
    
    if (!results || results.length === 0) {
        container.innerHTML = '<div class="text-gray-500 p-4 bg-gray-50 rounded">No arrangement data available</div>';
        return;
    }
    
    // Display each rail type as a separate section
    results.forEach(arrangement => {
        // Create group header for each rail type
        const groupHeader = document.createElement('div');
        groupHeader.className = 'mt-8 mb-4';
        groupHeader.innerHTML = `
            <h2 class="text-xl font-bold text-gray-800 border-b-2 border-blue-500 pb-2">
                ${arrangement.rail_type} Rails
            </h2>
            <p class="text-sm text-gray-600 mt-2">
                Material: ${arrangement.material_spec_used}
            </p>
        `;
        container.appendChild(groupHeader);
        
        // Create container for this rail type's details
        const railTypeContainer = document.createElement('div');
        railTypeContainer.className = 'bg-white rounded shadow p-4 mb-8';
        
        let html = `
            <div class="mb-4">
                <div class="text-sm text-gray-600">
                    Total Length: ${arrangement.total_required_length}mm | 
                    Total Quantity: ${arrangement.total_quantity}
                </div>
            </div>
        `;
        
        // Process rail details
        if (!arrangement.rail_details || arrangement.rail_details.length === 0) {
            html += `
                <div class="text-red-500 p-2">
                    No rail details available for this rail type
                </div>
            `;
        } else {
            arrangement.rail_details.forEach((rail, railIndex) => {
                // Ensure positions array exists and is valid
                if (!rail.positions_mm || rail.positions_mm.length === 0) {
                    console.warn(`No positions data for rail ${rail.rail_number}`);
                    return;
                }
                
                // Use rail_total_length if available, otherwise use sum of utilized + remaining
                const totalLength = rail.rail_total_length || (rail.utilized_length + rail.remaining_length);
                
                // Create a mapping of MOs to their estimated lengths
                const moLengthMap = getMoLengths(rail);
                
                html += `
                    <div class="rail-label flex justify-between text-sm mb-1 font-medium">
                        <span>Rail ${rail.rail_number} (${rail.utilization_rate}%) - ${totalLength}mm</span>
                        ${rail.remaining_length ? `<span>${rail.remaining_length}mm unused</span>` : ''}
                    </div>
                    <div class="rail-container" id="rail-${railIndex}-${arrangement.rail_type}">
                `;

                // Add pitch holes if available
                if (rail.hole_positions) {
                    rail.hole_positions.forEach(position => {
                        const positionPercent = (position / totalLength) * 100;
                        html += `
                            <div class="pitch-hole" style="left: ${positionPercent}%"></div>
                        `;
                    });
                }

                // Replace the current rail piece and unused space code with this updated version
                // Calculate the starting position of the first cut
                const firstCutPosition = rail.positions_mm.length > 0 ? Math.min(...rail.positions_mm) : 0;
                const usedLength = totalLength - rail.remaining_length - firstCutPosition;

                // Add a container that holds all rail parts to ensure no gaps
                html += `<div style="position: relative; width: 100%; height: 100%;">`;

                // Add unused space at beginning if there is any
                if (firstCutPosition > 0) {
                    html += `
                        <div class="unused-space" style="
                            left: 0;
                            width: ${(firstCutPosition / totalLength) * 100}%;
                            border-radius: 2px 0 0 2px;
                        ">
                        </div>
                    `;
                }

                // Add the rail piece (now positioned and sized correctly)
                html += `
                    <div class="rail-piece" style="
                        left: ${(firstCutPosition / totalLength) * 100}%;
                        width: ${(usedLength / totalLength) * 100}%;
                        border-radius: 0;
                    "></div>
                `;

                // Add unused space at end if there is any
                if (rail.remaining_length && rail.remaining_length > 0) {
                    html += `
                        <div class="unused-space" style="
                            left: ${((firstCutPosition + usedLength) / totalLength) * 100}%;
                            width: ${(rail.remaining_length / totalLength) * 100}%;
                            border-radius: 0 2px 2px 0;
                        "></div>
                    `;
                }

                html += `</div>`;

                // Only show actual cut positions (not both start and end)
                if (rail.positions_mm.length > 0) {
                    // Create a set of all unique cut positions
                    const cutPositions = new Set();
                    
                    // Add starting positions
                    rail.positions_mm.forEach(position => {
                        cutPositions.add(position);
                    });
                    
                    // Add the final position (end of utilized portion)
                    if (rail.utilized_length) {
                        cutPositions.add(rail.utilized_length);
                    }
                    
                    // Sort the positions and add dividers
                    Array.from(cutPositions).sort((a, b) => a - b).forEach(position => {
                        const positionPercent = (position / totalLength) * 100;
                        html += `
                            <div class="divider" style="left: ${positionPercent}%"></div>
                        `;
                    });
                }

                // Add MO labels
                const moCount = {};
                const moPositions = {};

                rail.mos.forEach((mo, index) => {
                    if (!moCount[mo]) {
                        moCount[mo] = 0;
                        moPositions[mo] = [];
                    }
                    moCount[mo]++;
                    moPositions[mo].push(rail.positions_mm[index]);
                });

                const uniqueMos = Object.keys(moCount);

                uniqueMos.forEach((mo, idx) => {
                    const positions = moPositions[mo];
                    const count = moCount[mo];
                    
                    // Calculate center position for label
                    const centerPos = positions[Math.floor(positions.length / 2)] + 50;
                    const positionPercent = (centerPos / totalLength) * 100;
                    
                    // Alternate labels above/below rail to avoid overlap
                    const isAlt = idx % 2 === 1;
                    const labelClass = isAlt ? 'mo-label mo-label-alt' : 'mo-label';
                    
                    // Show count in parentheses if more than 1
                    const quantityText = count > 1 ? ` (${count})` : '';
                    
                    html += `
                        <div class="${labelClass}" style="left: ${positionPercent}%">
                            ${mo}${quantityText}
                        </div>
                    `;
                });

                html += `</div>`;
            });
        }
        
        html += `</div>`;
        railTypeContainer.innerHTML = html;
        container.appendChild(railTypeContainer);
    });
    
    // Add a summary at the end
    const summaryDiv = document.createElement('div');
    summaryDiv.className = 'mt-8 p-4 bg-blue-50 rounded';
    summaryDiv.innerHTML = `
        <h3 class="font-bold mb-2">Summary</h3>
        <p>Total rail types: ${results.length}</p>
        <p>Total rails: ${results.reduce((sum, arr) => sum + (arr.rail_details ? arr.rail_details.length : 0), 0)}</p>
    `;
    container.appendChild(summaryDiv);
}

// Load all available items when the page loads
document.addEventListener('DOMContentLoaded', function() {
    loadAvailableItems();
});