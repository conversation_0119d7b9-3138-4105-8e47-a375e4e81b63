/* Simplified Rail Visualization CSS */
.rail-container {
    height: 16px;
    background: transparent; /* Change to transparent background */
    position: relative;
    border-radius: 2px;
    margin: 16px 0 2px 0;
    width: 100%;
    overflow: visible;
}

/* Blue ONLY for utilized rail space */
.rail-piece {
    position: absolute;
    height: 100%;
    background-color: #3b82f6; /* Blue */
    z-index: 1;
    margin: 0; /* Remove any margin */
}

.unused-space {
    position: absolute;
    height: 100%;
    background-color: #fca5a5; /* Pink */
    z-index: 1;
    margin: 0; /* Remove any margin */
}

/* Black dividers for all cut positions */
.divider {
    position: absolute;
    height: 24px;
    width: 2px;
    background-color: #111827; /* Black */
    top: -4px;
    z-index: 3;
    margin-left: -1px;
}

/* Subtler dividers for same MO */
.cut-divider {
    position: absolute;
    height: 16px;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 2;
}

/* Pitch holes */
.pitch-hole {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: white;
    border: 1px solid #111827;
    border-radius: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
}

/* MO labels */
.mo-label {
    position: absolute;
    font-size: 0.8rem;
    font-weight: 500;
    top: -20px;
    transform: translateX(-50%);
    white-space: nowrap;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 0px 3px;
    border-radius: 2px;
    z-index: 4;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mo-label-alt {
    top: 20px;
}

.rail-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 1rem;
    margin-bottom: 0.25rem;
    color: #374151;
}

/* Filter container styles */
.filter-container {
    width: 100%;
    margin: 0 auto 20px auto;
    max-width: 100%;
}

/* Filter token styles */
.token {
    display: inline-flex;
    align-items: center;
    background-color: #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    margin: 0.25rem;
}

.token-delete {
    margin-left: 0.5rem;
    cursor: pointer;
    font-weight: bold;
}
/* Add this to your CSS file to make cut positions more obvious */
.divider::after {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: #111827;
    border-radius: 50%;
    bottom: -10px;
    left: -1.5px;
}
.mo-type {
    font-size: 0.7rem;
    color: #6b7280;
    display: block; /* Change to block to ensure it's visible */
    margin-top: 2px; /* Add some spacing */
    font-weight: normal;
}
.token .mo-type {
    display: block;
    font-size: 0.65rem;
    color: #6b7280;
    margin-top: -2px;
    line-height: 1;
}
/* Material Specs Modal Styles */
.editable-input {
    width: 100%;
    padding: 0.25rem 0.5rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    background-color: transparent;
}

.editable-input:focus {
    border-color: #3b82f6;
    outline: none;
    background-color: #f9fafb;
}

tr.modified {
    background-color: #fffbeb;
}