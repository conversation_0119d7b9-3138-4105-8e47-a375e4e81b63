from db import update_postgres, call_db_json, config


def get_mo_details(mo_number: str):
    """
    Retrieve manufacturing order details from database
    Returns: dict with item_description, product_length, product_g1, product_pitch, order_quantity
    """
    query = f'''
    SELECT item_description, product_length, product_g1, product_pitch, order_quantity 
    FROM public.manufacturing_orders 
    WHERE order_number = '{mo_number}'
    '''
    try:
        result = call_db_json(query)
        return result[0] if result else None
    except Exception as e:
        raise RuntimeError(f"Failed to fetch MO details: {str(e)}") from e

mo_details = get_mo_details("M386082")
mo_details

import pandas as pd  # Import pandas to handle dataframes
from db import call_db_json  # Ensure to import the necessary function

query = f'''
SELECT mo.order_number, mo.reference_number, mo.item_description, mo.product_family,
       mo.product_model, mo.product_variant, mo.product_length, mo.product_g1,
       mo.product_pitch, mo.order_quantity,
       STRING_AGG(mol.facility_id, ', ') AS facilities,
       EXTRACT(EPOCH FROM (MAX(mol.time_out) - MIN(mol.time_out)))/3600 AS duration_hours  -- Calculate duration in hours
FROM public.manufacturing_orders mo
INNER JOIN public.manufacturing_order_logs mol ON mo.order_number = mol.order_number
WHERE mo.order_status = 55
  AND mo.order_release_code = 5
  AND mo.item_description NOT LIKE '%BLOCK%'
  AND mo.reference_number LIKE 'HK%'
  AND mo.product_family NOT LIKE '%UNIT%'
  AND (mo.printed_due >= NOW() - INTERVAL '4 years' OR mo.order_original_due >= NOW() - INTERVAL '4 years')
GROUP BY mo.order_number, mo.reference_number, mo.item_description, mo.product_family,
         mo.product_model, mo.product_variant, mo.product_length, mo.product_g1,
         mo.product_pitch, mo.order_quantity;
'''

result = call_db_json(query)  # Execute the query
df = pd.DataFrame(result)  # Convert result to DataFrame
df
# Save DataFrame to a CSV file
df.to_csv('manufacturing_orders.csv', index=False)

# Save DataFrame to an Excel file
# df.to_excel('manufacturing_orders.xlsx', index=False)

import pandas as pd  # Import pandas to handle dataframes
from db import call_db_json  # Ensure to import the necessary function

query = f'''
SELECT 
    mol.employee_number,
    CONCAT(mol.employee_first_name, ' ', mol.employee_last_name) AS employee_name,
    DATE(mol.time_out) AS processing_date,
    mol.facility_id,
    mol.machine_id,
    SUM(mol.quantity_complete) AS total_quantity_complete,
    CASE
        WHEN EXTRACT(HOUR FROM mol.time_out) >= 7 AND EXTRACT(HOUR FROM mol.time_out) < 15 THEN 'First'  -- 07:00 to 15:00
        WHEN EXTRACT(HOUR FROM mol.time_out) >= 15 AND EXTRACT(HOUR FROM mol.time_out) < 23 THEN 'Second'  -- 15:00 to 23:00
        ELSE 'third'  -- 23:00 to 07:00 (11 PM to 7 AM)
    END AS shift
FROM
    public.manufacturing_order_logs mol
INNER JOIN
    public.manufacturing_orders mo ON mol.order_number = mo.order_number
WHERE
    mo.item_description ILIKE '%BLOCK%'  -- Check for BLOCK in item_description
    AND DATE(mol.time_out) >= '2024-01-01'  -- Include only records from the year 2024
    AND DATE(mol.time_out) < '2025-01-01'   -- Exclude records from the year 2025
GROUP BY
    mol.employee_number,
    mol.employee_first_name,
    mol.employee_last_name,
    mol.facility_id,
    mol.machine_id,
    DATE(mol.time_out),  -- Include the date in the GROUP BY clause
    shift  -- Include the shift in the GROUP BY clause
ORDER BY 
    processing_date, 
    mol.employee_number;
'''

result = call_db_json(query)  # Execute the query
df = pd.DataFrame(result)  # Convert result to DataFrame
df
# Save DataFrame to a CSV file
df.to_csv('machine_performance_perday.csv', index=False)

# Save DataFrame to an Excel file
# df.to_excel('manufacturing_orders.xlsx', index=False)



import pandas as pd
import matplotlib.pyplot as plt

# Load the Excel file
df = pd.read_excel('rail-lead.xlsx')

# Group by product_family and product_model, then calculate min, max, mean of duration_hours
summary = df.groupby(['product_family', 'product_model'])['duration_hours'].agg(['min', 'max', 'mean']).reset_index()

# Convert hours to days and add new columns with appropriate names
summary['min_days'] = summary['min'] / 24
summary['max_days'] = summary['max'] / 24
summary['mean_days'] = summary['mean'] / 24

# Reorganize columns to have hours and days side by side
summary = summary[['product_family', 'product_model', 
                   'min', 'min_days', 
                   'max', 'max_days', 
                   'mean', 'mean_days']]

# Display the summary
print(summary)

# Plotting
# Create a figure and axis
fig, ax = plt.subplots(figsize=(12, 6))

# Plotting mean duration_hours against product_length and order_quantity
for key, grp in df.groupby(['product_family']):
    ax.scatter(grp['product_length'], grp['duration_hours'], label=f"{key} - Mean Duration", alpha=0.5)

# Adding labels and title
ax.set_xlabel('Product Length')
ax.set_ylabel('Duration Hours')
ax.set_title('Duration Hours vs Product Length by Product Family')
ax.legend()

# Show the plot
plt.show()

import pandas as pd
from tabulate import tabulate

# Load the Excel file
df = pd.read_excel('rail-lead.xlsx')

# Assuming 'facilities' is the column with facility names
# Remove duplicates and create new columns
df['unique_facilities'] = df['facilities'].apply(lambda x: ', '.join(set(x.split(', '))))
# df['facility_count'] = df['facilities'].apply(lambda x: len(set(x.split(','))))

# Replace the original facilities column with the unique facilities
df['facilities'] = df['unique_facilities']
df['facility_count'] = df['facilities'].apply(lambda x: len(set(x.split(','))))
# Drop the temporary columns if you no longer need them
df.drop(columns=['unique_facilities'], inplace=True)
df['facilities'] = df['facilities'].apply(lambda x: ', '.join(sorted(x.split(', '))))
df['first_facility'] = df['facilities'].apply(lambda x: sorted(x.split(', '))[0] if x else None)
# Delete records with facility IDs containing 'H'
# Print the count of remaining records
# print(f"\nNumber of remaining records: {df.shape[0]}")
df = df[~df['facilities'].str.contains('H', na=False)]
# Display the updated DataFrame in a nice tabular format
# print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))
# Print the count of remaining records
# print(f"\nNumber of remaining records: {df.shape[0]}")
# Create a new column 'fac-final' based on the conditions specified
df['fac-final'] = df['first_facility'].apply(
    lambda x: 'B0010' if x and (x.startswith('A') or x.startswith('B')) else
               'C0010' if x and x.startswith('C') else
               'D0010'
)

# Display the updated DataFrame to see the new column
# print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))
import pandas as pd
import numpy as np

# Assuming df is already defined and contains the necessary data
# Fill NaN values in duration_hours with 0
df['duration_hours'] = df['duration_hours'].fillna(0)

# Create a list to store summary data
summary_data = []
# Create a new column to store original duration hours for comparison
df['original_duration_hours'] = df['duration_hours'].copy()

# Normalize duration_hours based on frequency distribution
for (family, model), group in df.groupby(['product_family', 'product_model']):
    # Calculate statistics for the current group
    min_value = group['duration_hours'].min()
    max_value = group['duration_hours'].max()
    mean_value = group['duration_hours'].mean()
    
    # Calculate histogram
    counts, bin_edges = np.histogram(group['duration_hours'], bins=30)
    
    # Determine the skewness
    if counts[0] > counts[-1]:  # More frequency on the left side
        # Adjust both sides
        max_adjustment = (max_value - mean_value) * 0.2  # Decrease max values
        min_adjustment = (mean_value - min_value) * 0.2  # Increase min values
    elif counts[-1] > counts[0]:  # More frequency on the right side
        # Adjust both sides
        min_adjustment = (mean_value - min_value) * 0.2  # Increase min values
        max_adjustment = (max_value - mean_value) * 0.2  # Decrease max values
    else:
        # If balanced, apply a small adjustment to both
        min_adjustment = (mean_value - min_value) * 0.1  # Increase min values
        max_adjustment = (max_value - mean_value) * 0.1  # Decrease max values

    # Calculate new min and max targets
    new_min_value = min_value + min_adjustment
    new_max_value = max_value - max_adjustment

    # Ensure min values do not go negative
    if new_min_value < 0:
        new_min_value = 0

    # Apply proportional scaling to each value in the group
    # This preserves the relative positions of values while compressing the range
    group_indices = df.index[(df['product_family'] == family) & (df['product_model'] == model)]
    
    for idx in group_indices:
        original_value = df.at[idx, 'duration_hours']
        
        # Skip if original value is exactly min or max to avoid division by zero
        if original_value == min_value and original_value == max_value:
            continue
        elif original_value == min_value:
            df.at[idx, 'duration_hours'] = new_min_value
        elif original_value == max_value:
            df.at[idx, 'duration_hours'] = new_max_value
        else:
            # Calculate position of this value in the original range (0 to 1)
            position = (original_value - min_value) / (max_value - min_value)
            # Apply that position to the new range
            df.at[idx, 'duration_hours'] = new_min_value + position * (new_max_value - new_min_value)

    # Append the summary data
    summary_data.append({
        'Product Family': family,
        'Product Model': model,
        'Original Min': min_value,
        'Original Max': max_value,
        'New Min': new_min_value,
        'New Max': new_max_value,
        'Mean': mean_value,
        'Count': len(group)
    })

# Create a DataFrame from the summary data
summary_df = pd.DataFrame(summary_data)

# Display the summary table showing before and after statistics
print(summary_df)

# # Add code to analyze the changes
# print("\nSample of records with original and normalized duration_hours:")
# sample_df = df.sample(min(10, len(df)))
# sample_comparison = sample_df[['product_family', 'product_model', 'original_duration_hours', 'duration_hours']]
# print(sample_comparison)

# # Calculate overall changes
# print("\nOverall changes in duration_hours:")
# print(f"Original range: {df['original_duration_hours'].min()} to {df['original_duration_hours'].max()}")
# print(f"New range: {df['duration_hours'].min()} to {df['duration_hours'].max()}")
# print(f"Original mean: {df['original_duration_hours'].mean():.2f}")
# print(f"New mean: {df['duration_hours'].mean():.2f}")

# Save the updated DataFrame to a CSV file
# df.to_csv('final_normalized.csv', index=False)

summary_data_new = []
for (family, model), group in df.groupby(['product_family', 'product_model']):
    # Calculate statistics for the current group
    min_value = group['duration_hours'].min()
    max_value = group['duration_hours'].max()
    mean_value = group['duration_hours'].mean()
    
    
    summary_data_new.append({
            'Product Family': family,
            'Product Model': model,
            'Original Min': min_value,
            'Original Max': max_value,
            # 'New Min': new_min_value,
            # 'New Max': new_max_value,
            'Mean': mean_value,
            'Count': len(group)
    })

# Create a DataFrame from the summary data
summary_data_new = pd.DataFrame(summary_data_new)
print(summary_data_new)

df = pd.read_csv('final_normalized.csv')
# print(df)

import pandas as pd

# Assuming df is already defined and contains the necessary data

# Analysis for categorical columns
categorical_columns = ['product_family', 'product_model', 'product_variant', 'fac-final']

# Create a summary DataFrame to hold the analysis results
analysis_results = []

for column in categorical_columns:
    value_counts = df[column].value_counts(dropna=False)
    null_count = df[column].isnull().sum()
    unique_categories = df[column].nunique()
    categories = df[column].unique()
    
    analysis_results.append({
        'Column': column,
        'Total Values': len(df[column]),
        'Null Values': null_count,
        'Unique Categories': unique_categories,
        'Categories': categories
    })

# Create a DataFrame from the analysis results
categorical_summary_df = pd.DataFrame(analysis_results)

# Display the categorical summary
print("Categorical Summary:")
print(categorical_summary_df)

# Analysis for numerical columns: facility_count and duration_hours
numerical_columns = ['facility_count', 'duration_hours']

numerical_summary = df[numerical_columns].agg(['min', 'max', 'mean']).reset_index()
numerical_summary.columns = ['Metric'] + list(numerical_columns)

# Display the numerical summary
print("\nNumerical Summary:")
print(numerical_summary)

# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import xgboost as xgb
import logging
import joblib
from datetime import datetime
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

# ---------------------------
# Part 1: Data Loading and Preprocessing
# ---------------------------

def load_and_preprocess_data(filepath='final_normalized.csv'):
    """Load and preprocess the dataset"""
    # Load data
    logger.info(f"Loading data from {filepath}")
    df = pd.read_csv(filepath)
    logger.info(f"Loaded data with shape: {df.shape}")
    
    # Examine target variable
    logger.info("\nTarget variable statistics:")
    logger.info(df['duration_hours'].describe())
    
    # Check for skewness in target variable
    skewness = df['duration_hours'].skew()
    logger.info(f"\nSkewness of duration_hours: {skewness:.2f}")
    
    # Apply log transformation for skewed distribution
    apply_log = skewness > 1.5
    if apply_log:
        logger.info("Target variable is skewed, applying log transformation")
        df['duration_hours_log'] = np.log1p(df['duration_hours'])
        y = df['duration_hours_log']
    else:
        y = df['duration_hours']
    
    # Select features
    X = df[['product_family', 'product_model', 'product_variant', 
            'product_length', 'order_quantity', 'fac-final', 'facility_count']]
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Print categorical feature information
    cat_cols = ['product_family', 'product_model', 'product_variant', 'fac-final']
    logger.info("\nCategorical Feature Summary:")
    for col in cat_cols:
        n_unique = X[col].nunique()
        n_null = X[col].isnull().sum()
        logger.info(f"{col}: {n_unique} unique values, {n_null} null values")
    
    # Define categorical and numerical columns
    num_cols = ['product_length', 'order_quantity', 'facility_count']
    
    # Create preprocessor
    categorical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='constant', fill_value='missing')),
        ('onehot', OneHotEncoder(handle_unknown='ignore'))
    ])
    
    numerical_transformer = Pipeline(steps=[
        ('imputer', SimpleImputer(strategy='median')),
        ('scaler', StandardScaler())
    ])
    
    preprocessor = ColumnTransformer(
        transformers=[
            ('cat', categorical_transformer, cat_cols),
            ('num', numerical_transformer, num_cols)
        ])
    
    return X_train, X_test, y_train, y_test, preprocessor, apply_log

# ---------------------------
# Part 2: Model Training
# ---------------------------

def train_gpu_model(X_train, X_test, y_train, y_test, preprocessor, apply_log):
    """Train with optimized GPU settings"""
    logger.info("\n===== Training GPU-Optimized XGBoost Model =====")
    
    # Create XGBoost model with correct GPU parameters
    xgb_model = xgb.XGBRegressor(
        tree_method='hist',      
        device='cuda',           # Use GPU
        random_state=42,
        n_estimators=200,
        learning_rate=0.1,
        max_depth=7,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.01,
        reg_lambda=1.0
    )
    
    # Create pipeline
    pipeline = Pipeline(steps=[
        ('preprocessor', preprocessor),
        ('model', xgb_model)
    ])
    
    # Train model
    start_time = datetime.now()
    logger.info("Training optimized XGBoost model...")
    
    pipeline.fit(X_train, y_train)
    
    # Make predictions
    y_pred = pipeline.predict(X_test)
    
    # Convert predictions back if log transformation was applied
    if apply_log:
        y_test_original = np.expm1(y_test)
        y_pred_original = np.expm1(y_pred)
    else:
        y_test_original = y_test
        y_pred_original = y_pred
    
    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_test_original, y_pred_original))
    mae = mean_absolute_error(y_test_original, y_pred_original)
    r2 = r2_score(y_test_original, y_pred_original)
    
    # Report results
    training_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Optimized XGBoost - RMSE: {rmse:.2f}, MAE: {mae:.2f}, R²: {r2:.4f}")
    logger.info(f"Training time: {training_time:.2f} seconds")
    
    return pipeline, rmse, mae, r2

# ---------------------------
# Part 3: Feature Importance Analysis
# ---------------------------

def analyze_feature_importance(model, preprocessor):
    """Analyze feature importance for the model"""
    logger.info(f"\n===== Feature Importance Analysis =====")
    
    try:
        # Get feature names
        categorical_feature_names = preprocessor.transformers_[0][1].named_steps['onehot'].get_feature_names_out([
            'product_family', 'product_model', 'product_variant', 'fac-final'
        ])
        numeric_feature_names = ['product_length', 'order_quantity', 'facility_count']
        all_feature_names = np.concatenate([categorical_feature_names, numeric_feature_names])
        
        # Get feature importances
        model_step = model.named_steps['model']
        feature_importances = model_step.feature_importances_
        
        # Sort features by importance
        indices = np.argsort(feature_importances)[::-1]
        
        # Print top features
        logger.info("\nTop 15 most important features:")
        for i in range(min(15, len(all_feature_names))):
            logger.info(f"{i+1}. {all_feature_names[indices[i]]}: {feature_importances[indices[i]]:.4f}")
        
        # Top important features
        top_features = [all_feature_names[i] for i in indices[:10]]
        logger.info(f"\nTop features that drive most of the predictions:")
        logger.info(", ".join(top_features))
        
        # Create feature importance plot
        plt.figure(figsize=(10, 6))
        plt.title("Feature Importances")
        plt.barh(range(15), feature_importances[indices[:15]], align="center")
        plt.yticks(range(15), [all_feature_names[i] for i in indices[:15]])
        plt.xlabel("Relative Importance")
        plt.tight_layout()
        plt.savefig("feature_importance.png")
        logger.info("Feature importance plot saved to 'feature_importance.png'")
        
        return [(all_feature_names[i], feature_importances[i]) for i in indices[:15]]
    except Exception as e:
        logger.error(f"Error analyzing feature importance: {str(e)}")
        return None

# ---------------------------
# Part 4: Model Saving and Loading
# ---------------------------

def save_model(model, apply_log, model_dir='model'):
    """Save the trained model and configuration"""
    # Create directory if it doesn't exist
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    
    # Save the model
    model_path = os.path.join(model_dir, 'duration_model.joblib')
    joblib.dump(model, model_path)
    
    # Save the configuration
    config_path = os.path.join(model_dir, 'model_config.txt')
    with open(config_path, 'w') as f:
        f.write(f"apply_log={apply_log}")
    
    logger.info(f"Model saved to {model_path}")
    logger.info(f"Configuration saved to {config_path}")
    
    return model_path, config_path

def load_model(model_dir='model'):
    """Load the model and configuration"""
    # Load the model
    model_path = os.path.join(model_dir, 'duration_model.joblib')
    model = joblib.load(model_path)
    
    # Load the configuration
    config_path = os.path.join(model_dir, 'model_config.txt')
    apply_log = True  # Default
    
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            for line in f:
                if line.startswith('apply_log='):
                    apply_log = line.strip().split('=')[1].lower() == 'true'
    
    logger.info(f"Model loaded from {model_path}")
    logger.info(f"apply_log={apply_log}")
    
    return model, apply_log

# ---------------------------
# Part 5: Prediction Function
# ---------------------------

def create_prediction_function(model, apply_log):
    """Create a function to make predictions on new data"""
    def predict_duration_hours(new_data):
        """Predict duration_hours for new data"""
        # Check required columns
        required_cols = ['product_family', 'product_model', 'product_variant', 
                        'product_length', 'order_quantity', 'fac-final']
        missing_cols = set(required_cols) - set(new_data.columns)
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Add facility_count if missing
        if 'facility_count' not in new_data.columns:
            logger.info("Adding dummy facility_count for prediction")
            new_data = new_data.copy()
            new_data['facility_count'] = np.nan
        
        # Make predictions
        predictions = model.predict(new_data)
        
        # Convert back if log transformation was applied
        if apply_log:
            predictions = np.expm1(predictions)
        
        return predictions
    
    return predict_duration_hours

# ---------------------------
# Part 6: Main Training Function
# ---------------------------

def train_and_save_model(data_path='final_normalized.csv', model_dir='model'):
    """Train, evaluate, and save the model"""
    logger.info("===== Starting Duration Hours Prediction Model Training =====")
    
    # 1. Load and preprocess data
    X_train, X_test, y_train, y_test, preprocessor, apply_log = load_and_preprocess_data(data_path)
    
    # 2. Train GPU model
    model, rmse, mae, r2 = train_gpu_model(X_train, X_test, y_train, y_test, preprocessor, apply_log)
    
    # 3. Analyze feature importance
    feature_importances = analyze_feature_importance(model, preprocessor)
    
    # 4. Save the model
    model_path, config_path = save_model(model, apply_log, model_dir)
    
    # 5. Example prediction
    logger.info("\n===== Example Prediction =====")
    test_data = pd.DataFrame({
        'product_family': ['HSR', 'SHS'],
        'product_model': ['65', '55'],
        'product_variant': [None, 'W'],
        'product_length': [120, 180],
        'order_quantity': [10, 25],
        'fac-final': ['D0010', 'C0010']
    })
    
    predict_function = create_prediction_function(model, apply_log)
    predictions = predict_function(test_data)
    
    logger.info("\nExample Predictions:")
    for i, pred in enumerate(predictions):
        logger.info(f"Sample {i+1}: Predicted duration = {pred:.2f} hours")
    
    # 6. Summary
    logger.info("\n===== Model Summary =====")
    logger.info(f"Final model RMSE: {rmse:.2f}")
    logger.info(f"Final model MAE: {mae:.2f}")
    logger.info(f"Final model R²: {r2:.4f}")
    logger.info(f"Log transformation applied: {'Yes' if apply_log else 'No'}")
    
    return {
        'model': model,
        'model_path': model_path,
        'rmse': rmse, 
        'mae': mae, 
        'r2': r2, 
        'apply_log': apply_log,
        'feature_importances': feature_importances
    }

# ---------------------------
# Part 7: Prediction Module
# ---------------------------

def make_predictions(input_data, model_dir='model'):
    """
    Make predictions using the saved model
    
    Parameters:
    -----------
    input_data : pandas DataFrame or string
        Either a DataFrame with required columns or a path to a CSV file
    model_dir : string
        Directory containing the saved model files
        
    Returns:
    --------
    predictions : numpy array
        Predicted duration hours
    """
    # Load the model and configuration
    model, apply_log = load_model(model_dir)
    
    # Load data if input is a string (file path)
    if isinstance(input_data, str):
        logger.info(f"Loading prediction data from {input_data}")
        input_data = pd.read_csv(input_data)
    
    # Create prediction function
    predict_function = create_prediction_function(model, apply_log)
    
    # Make predictions
    predictions = predict_function(input_data)
    
    return predictions

# ---------------------------
# Part 8: Example Usage
# ---------------------------

def main():
    """Main function to demonstrate the entire workflow"""
    # Part A: Training workflow
    results = train_and_save_model(data_path='final_normalized.csv', model_dir='model')
    
    # Part B: Prediction workflow (using saved model)
    logger.info("\n===== Using Saved Model for Predictions =====")
    
    # Example new data
    new_data = pd.DataFrame({
        'product_family': ['SHS', 'HSR', 'SR'],
        'product_model': ['15', '35', '25'],
        'product_variant': [None, 'XLC', 'W'],
        'product_length': [400, 250, 180],
        'order_quantity': [9, 15, 5],
        'fac-final': ['D0010', 'C0010', 'B0010']
    })
    
    # Make predictions with saved model
    predictions = make_predictions(new_data, model_dir='model')
    
    # Display predictions
    for i, pred in enumerate(predictions):
        logger.info(f"New sample {i+1}: Predicted duration = {pred:.2f} hours")
    
    logger.info("\n===== Workflow Complete =====")

if __name__ == "__main__":
    main()

import pandas as pd  # Import pandas to handle dataframes
from db import call_db_json
query = f'''
SELECT
    hru.reference_number,
    hru.item_description,
    mo.product_family AS family,
    mo.product_model AS model,
    mo.product_variant AS variant,
    mo.product_length AS length,
    SUM(mo.order_quantity) AS total_quantity
FROM
    hk_rail_unreleased hru
JOIN
    manufacturing_orders mo
    ON hru.reference_number = mo.reference_number
    AND mo.order_number = ANY(hru.mos)
GROUP BY
    hru.reference_number,
    hru.item_description,
    mo.product_family,
    mo.product_model,
    mo.product_variant,
    mo.product_length
ORDER BY
    hru.reference_number,
    hru.item_description,
    mo.product_family,
    mo.product_model,
    mo.product_variant,
    mo.product_length;
'''

result = call_db_json(query)  # Execute the query
df = pd.DataFrame(result)  # Convert result to DataFrame
display(df)

import pandas as pd  # Import pandas to handle dataframes
from db import call_db_json
query = f'''
SELECT
  t1.item_description,
  STRING_AGG(DISTINCT t2.facility_id, ', ') AS facility_ids
FROM (
  SELECT
    item_description,
    MIN(order_number) AS order_number,
    product_family,
    product_model,
    product_variant
  FROM public.manufacturing_orders t
  WHERE item_description IS NOT NULL
    AND order_number IS NOT NULL
    AND product_family IN ('HDR', 'HRW', 'HSR', 'SHS', 'SHW', 'SR', 'SRS', 'SRS-W')
    AND item_description NOT LIKE '[0-9]%'
    AND item_description LIKE '%RAIL%'
  GROUP BY item_description, product_family, product_model, product_variant
) t1
LEFT JOIN public.manufacturing_order_logs t2
  ON t1.order_number = t2.order_number
GROUP BY
  t1.item_description,
  t1.order_number
ORDER BY t1.order_number;
'''

result = call_db_json(query)  # Execute the query
df = pd.DataFrame(result)  # Convert result to DataFrame
display(df)

def preprocess_data(df):
    """
    Preprocess the manufacturing data
    """
    df_processed = df.copy()
    #df consists of only 2 columns item_description and facility_ids
    #item description look like this with diff names: SRS12W-390LM RAIL	, SRS12-470LM RAIL,SRS12W-190LM RAIL	
    #facility_id looks like this : D0010, D0020, F0010  
    # OR like this: C0010, C0020, C0060, C0080, F0010, K0010, K0020
    # OR like this: D0010
    
    # Remove 'RAIL' from the end of item_description if it exists
    df_processed['item_description_clean'] = df_processed['item_description'].str.replace(r'\s+RAIL$', '', regex=True)
    
    # Split description into FMV (before first '-') and detail (after first '-')
    df_processed[['FMV', 'detail']] = df_processed['item_description_clean'].str.split('-', n=1, expand=True)
    
    # Handle cases where there might not be a '-' 
    df_processed['FMV'] = df_processed['FMV'].fillna(df_processed['item_description_clean'])
    df_processed['detail'] = df_processed['detail'].fillna('')
    
    # Clean and split facility_ids into lists
    df_processed['facility_list'] = df_processed['facility_ids'].str.split(', ')
    df_processed['Family'] = df_processed['FMV'].str.extract(r'^([A-Za-z]+)')
    df_processed['Model'] = df_processed['FMV'].str.extract(r'([0-9]+)').astype('Int64')
    df_processed['Variant'] = df_processed['FMV'].str.extract(r'[0-9]+([A-Za-z]+)')
    # Create additional validation columns to check extraction
    df_processed['FMV_reconstructed'] = (
        df_processed['Family'].fillna('') + 
        df_processed['Model'].astype(str).replace('nan', '').replace('<NA>', '') + 
        df_processed['Variant'].fillna('')
    )
    df_processed['detail_numeric'] = df_processed['detail'].str.extract(r'(\d+)').astype(float)
    df_processed['detail_alpha'] = df_processed['detail'].str.extract(r'([A-Za-z]+)')
    df_processed['detail_length'] = df_processed['detail'].str.len()
    df_processed['has_parentheses'] = df_processed['detail'].str.contains(r'\(.*\)', na=False)
    df_processed['has_GK'] = df_processed['detail'].str.contains('GK', na=False)
    df_processed['has_L'] = df_processed['detail'].str.contains('L', na=False)
    df_processed['has_LM'] = df_processed['detail'].str.contains('LM', na=False)
    df_processed['has_W'] = df_processed['FMV'].str.contains('W', na=False)
    df_processed = df_processed.drop(['item_description', 'facility_ids','FMV','FMV_reconstructed','detail'], axis=1)
    df_processed = df_processed[(df_processed['facility_list'].notnull()) & (df_processed['facility_list'].apply(lambda x: x is not None and len(x) > 0))]
    return df_processed
final=preprocess_data(df)

final.to_csv('des.csv', index=True)

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import MultiLabelBinarizer, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.multioutput import MultiOutputClassifier
from sklearn.metrics import classification_report, accuracy_score, hamming_loss, jaccard_score
import warnings
warnings.filterwarnings('ignore')

class FacilityPredictor:
    def __init__(self):
        self.mlb = MultiLabelBinarizer()
        self.label_encoders = {}
        self.model = None
        self.feature_columns = []
        
    def prepare_features(self, df):
        """
        Prepare features from the preprocessed dataframe
        """
        df_features = df.copy()
        
        # Categorical features that need encoding
        categorical_features = ['Family', 'Variant', 'detail_alpha']
        
        # Encode categorical features
        for feature in categorical_features:
            if feature not in self.label_encoders:
                self.label_encoders[feature] = LabelEncoder()
                df_features[f'{feature}_encoded'] = self.label_encoders[feature].fit_transform(
                    df_features[feature].fillna('Unknown').astype(str)
                )
            else:
                # Handle new categories during prediction
                try:
                    df_features[f'{feature}_encoded'] = self.label_encoders[feature].transform(
                        df_features[feature].fillna('Unknown').astype(str)
                    )
                except ValueError:
                    # If unknown category, assign -1
                    df_features[f'{feature}_encoded'] = -1
        
        # Define feature columns
        self.feature_columns = [
            'Model', 'detail_numeric', 'detail_length',
            'has_parentheses', 'has_GK', 'has_L', 'has_LM', 'has_W',
            'Family_encoded', 'Variant_encoded', 'detail_alpha_encoded'
        ]
        
        # Create feature matrix
        X = df_features[self.feature_columns].fillna(0)
        
        # Convert boolean columns to int
        boolean_cols = ['has_parentheses', 'has_GK', 'has_L', 'has_LM', 'has_W']
        for col in boolean_cols:
            X[col] = X[col].astype(int)
            
        return X
    
    def prepare_targets(self, df):
        """
        Prepare multi-label targets from facility_list
        """
        # Convert facility_list to binary matrix
        y_binary = self.mlb.fit_transform(df['facility_list'])
        return y_binary
    
    def train(self, df):
        """
        Train the facility prediction model
        """
        print("Preparing features...")
        X = self.prepare_features(df)
        
        print("Preparing targets...")
        y = self.prepare_targets(df)
        
        print(f"Feature matrix shape: {X.shape}")
        print(f"Target matrix shape: {y.shape}")
        print(f"Available facilities: {list(self.mlb.classes_)}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=None
        )
        
        # Train Random Forest with MultiOutput wrapper
        print("Training model...")
        rf = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            max_depth=10,
            min_samples_split=5,
            class_weight='balanced'
        )
        self.model = MultiOutputClassifier(rf)
        self.model.fit(X_train, y_train)
        
        # Evaluate
        print("Evaluating model...")
        y_pred = self.model.predict(X_test)
        
        self._evaluate_model(y_test, y_pred)
        
        # Feature importance
        self._show_feature_importance()
        
        return X_train, X_test, y_train, y_test, y_pred
    
    def _evaluate_model(self, y_test, y_pred):
        """
        Evaluate model performance
        """
        print("\n=== Model Performance ===")
        
        # Overall metrics
        hamming = hamming_loss(y_test, y_pred)
        jaccard = jaccard_score(y_test, y_pred, average='samples')
        
        print(f"Hamming Loss (lower is better): {hamming:.4f}")
        print(f"Jaccard Score (higher is better): {jaccard:.4f}")
        
        # Per-facility accuracy
        print(f"\n=== Per-Facility Accuracy ===")
        for i, facility in enumerate(self.mlb.classes_):
            accuracy = accuracy_score(y_test[:, i], y_pred[:, i])
            print(f"{facility}: {accuracy:.3f}")
    
    def _show_feature_importance(self):
        """
        Show feature importance
        """
        print(f"\n=== Feature Importance ===")
        # Average importance across all outputs
        feature_importance = np.mean([
            estimator.feature_importances_ for estimator in self.model.estimators_
        ], axis=0)
        
        importance_df = pd.DataFrame({
            'feature': self.feature_columns,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        print(importance_df)
    
    def predict(self, df_new):
        """
        Predict facilities for new data
        """
        if self.model is None:
            raise ValueError("Model not trained yet. Call train() first.")
        
        X_new = self.prepare_features(df_new)
        y_pred_binary = self.model.predict(X_new)
        
        # Convert binary predictions back to facility lists
        predicted_facilities = self.mlb.inverse_transform(y_pred_binary)
        
        return predicted_facilities
    
    def predict_single_item(self, family, model, variant, detail_numeric, detail_alpha, 
                          detail_length, has_parentheses, has_GK, has_L, has_LM, has_W):
        """
        Predict facilities for a single item
        """
        # Create dataframe for single prediction
        single_item = pd.DataFrame({
            'Family': [family],
            'Model': [model],
            'Variant': [variant],
            'detail_numeric': [detail_numeric],
            'detail_alpha': [detail_alpha],
            'detail_length': [detail_length],
            'has_parentheses': [has_parentheses],  
            'has_GK': [has_GK],
            'has_L': [has_L],
            'has_LM': [has_LM],
            'has_W': [has_W]
        })
        
        predicted_facilities = self.predict(single_item)[0]
        return list(predicted_facilities)

def load_and_demo():
    """
    Demo function with sample data
    """
    # Sample data based on your format
    # sample_data = {
    #     'facility_list': [
    #         ['D0010', 'D0020', 'F0010'],
    #         ['D0010', 'D0020', 'F0010'],
    #         ['D0010', 'D0020', 'F0010'],
    #         ['D0010', 'D0020', 'F0010'],
    #         ['C0010', 'C0020', 'C0060', 'C0080', 'F0010', 'K0010', 'K0020'],
    #         ['C0010', 'C0020', 'C0060', 'C0080', 'F0010', 'K0010', 'K0020'],
    #         ['C0010', 'C0020', 'C0060', 'C0080', 'C0090', 'F0010', 'K0010', 'K0020'],
    #         ['D0010', 'F0010', 'K0010', 'K0020']
    #     ],
    #     'Family': ['SRS', 'SRS', 'SRS', 'SRS', 'SHW', 'SHW', 'SR', 'SR'],
    #     'Model': [12, 12, 15, 12, 21, 35, 25, 35],
    #     'Variant': ['W', None, None, 'W', None, None, None, None],
    #     'detail_numeric': [390, 470, 150, 190, 580, 1560, 3000, 3000],
    #     'detail_alpha': ['LM', 'LM', 'LM', 'LM', 'L', 'L', 'LY', 'L'],
    #     'detail_length': [5, 5, 5, 5, 8, 8, 8, 8],
    #     'has_parentheses': [False, False, False, False, True, True, True, True],
    #     'has_GK': [False, False, False, False, True, True, True, True],
    #     'has_L': [True, True, True, True, True, True, True, True],
    #     'has_LM': [True, True, True, True, False, False, False, False],
    #     'has_W': [True, False, False, True, True, True, True, True]
    # }
    
    df = pd.DataFrame(final)
    display(df)
    return df

def main():
    """
    Main execution function
    """
    print("=== Manufacturing Facility Prediction Model ===")
    
    # Load data (replace with your actual data)
    print("Loading data...")
    df = load_and_demo()  # Replace with: df = your_preprocessed_dataframe
    
    print("Sample data:")
    print(df.head())
    
    # Initialize and train model
    predictor = FacilityPredictor()
    predictor.train(df)
    
    # Test predictions on new items
    print("\n=== Testing Predictions ===")
    
    # Test case 1: SRS family item
    test1_facilities = predictor.predict_single_item(
        family='SRS', model=20, variant='W', detail_numeric=250, 
        detail_alpha='LM', detail_length=5, has_parentheses=False,
        has_GK=False, has_L=True, has_LM=True, has_W=True
    )
    print(f"SRS20W-250LM → Predicted facilities: {test1_facilities}")
    
    # Test case 2: SHW family item  
    test2_facilities = predictor.predict_single_item(
        family='SHW', model=30, variant=None, detail_numeric=800,
        detail_alpha='L', detail_length=8, has_parentheses=True,
        has_GK=True, has_L=True, has_LM=False, has_W=False
    )
    print(f"SHW30-800L(GK) → Predicted facilities: {test2_facilities}")
    
    # Test case 3: New family (HDR)
    test3_facilities = predictor.predict_single_item(
        family='HDR', model=15, variant=None, detail_numeric=400,
        detail_alpha='LH', detail_length=6, has_parentheses=False,
        has_GK=False, has_L=True, has_LM=False, has_W=False
    )
    print(f"HDR15-400LH → Predicted facilities: {test3_facilities}")
    
    return predictor

if __name__ == "__main__":
    predictor = main()
    

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import MultiLabelBinarizer, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.multioutput import MultiOutputClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import classification_report, accuracy_score, hamming_loss, jaccard_score
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from scipy.sparse import hstack
import warnings
warnings.filterwarnings('ignore')

class EnhancedFacilityPredictor:
    def __init__(self, use_text_features=True, max_text_features=100):
        self.mlb = MultiLabelBinarizer()
        self.label_encoders = {}
        self.text_vectorizer = TfidfVectorizer(
            max_features=max_text_features,
            ngram_range=(1, 2),  # Include both unigrams and bigrams
            lowercase=True,
            stop_words=None  # Keep all words for technical descriptions
        ) if use_text_features else None
        self.model = None
        self.feature_columns = []
        self.use_text_features = use_text_features
        
    def prepare_features(self, df):
        """
        Prepare features from the preprocessed dataframe including text features
        """
        df_features = df.copy()
        
        # Categorical features that need encoding
        categorical_features = ['Family', 'Variant', 'detail_alpha']
        
        # Encode categorical features
        for feature in categorical_features:
            if feature not in self.label_encoders:
                self.label_encoders[feature] = LabelEncoder()
                df_features[f'{feature}_encoded'] = self.label_encoders[feature].fit_transform(
                    df_features[feature].fillna('Unknown').astype(str)
                )
            else:
                # Handle new categories during prediction
                known_classes = set(self.label_encoders[feature].classes_)
                df_features[f'{feature}_encoded'] = df_features[feature].fillna('Unknown').astype(str).apply(
                    lambda x: self.label_encoders[feature].transform([x])[0] if x in known_classes else -1
                )
        
        # Define numerical and categorical feature columns
        self.feature_columns = [
            'Model', 'detail_numeric', 'detail_length',
            'has_parentheses', 'has_GK', 'has_L', 'has_LM', 'has_W',
            'Family_encoded', 'Variant_encoded', 'detail_alpha_encoded'
        ]
        
        # Create feature matrix
        X_structured = df_features[self.feature_columns].fillna(0)
        
        # Convert boolean columns to int
        boolean_cols = ['has_parentheses', 'has_GK', 'has_L', 'has_LM', 'has_W']
        for col in boolean_cols:
            X_structured[col] = X_structured[col].astype(int)
        
        # Add text features if enabled
        if self.use_text_features and 'item_description_clean' in df_features.columns:
            # Handle missing text data
            text_data = df_features['item_description_clean'].fillna('').astype(str)
            
            if hasattr(self.text_vectorizer, 'vocabulary_'):  # Already fitted
                X_text = self.text_vectorizer.transform(text_data)
            else:  # First time fitting
                X_text = self.text_vectorizer.fit_transform(text_data)
            
            # Combine structured and text features
            X_combined = hstack([X_structured.values, X_text])
            return X_combined
        else:
            return X_structured.values
    
    def prepare_targets(self, df):
        """
        Prepare multi-label targets from facility_list
        """
        # Convert facility_list to binary matrix
        y_binary = self.mlb.fit_transform(df['facility_list'])
        return y_binary
    
    def train(self, df):
        """
        Train the facility prediction model
        """
        print("Preparing features...")
        X = self.prepare_features(df)
        
        print("Preparing targets...")
        y = self.prepare_targets(df)
        
        print(f"Feature matrix shape: {X.shape}")
        print(f"Target matrix shape: {y.shape}")
        print(f"Available facilities: {list(self.mlb.classes_)}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=None
        )
        
        # Train Random Forest with MultiOutput wrapper
        print("Training model...")
        rf = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            max_depth=15,  # Increased for text features
            min_samples_split=3,
            min_samples_leaf=2,
            class_weight='balanced'
        )
        self.model = MultiOutputClassifier(rf)
        self.model.fit(X_train, y_train)
        
        # Evaluate
        print("Evaluating model...")
        y_pred = self.model.predict(X_test)
        
        self._evaluate_model(y_test, y_pred)
        
        return X_train, X_test, y_train, y_test, y_pred
    
    def _evaluate_model(self, y_test, y_pred):
        """
        Evaluate model performance
        """
        print("\n=== Model Performance ===")
        
        # Overall metrics
        hamming = hamming_loss(y_test, y_pred)
        jaccard = jaccard_score(y_test, y_pred, average='samples')
        
        print(f"Hamming Loss (lower is better): {hamming:.4f}")
        print(f"Jaccard Score (higher is better): {jaccard:.4f}")
        
        # Per-facility accuracy
        print(f"\n=== Per-Facility Accuracy ===")
        for i, facility in enumerate(self.mlb.classes_):
            accuracy = accuracy_score(y_test[:, i], y_pred[:, i])
            support = np.sum(y_test[:, i])
            print(f"{facility}: {accuracy:.3f} (support: {support})")
    
    def predict(self, df_new):
        """
        Predict facilities for new data
        """
        if self.model is None:
            raise ValueError("Model not trained yet. Call train() first.")
        
        X_new = self.prepare_features(df_new)
        y_pred_binary = self.model.predict(X_new)
        
        # Convert binary predictions back to facility lists
        predicted_facilities = self.mlb.inverse_transform(y_pred_binary)
        
        return predicted_facilities
    
    def predict_with_probabilities(self, df_new, threshold=0.5):
        """
        Predict facilities with probability scores
        """
        if self.model is None:
            raise ValueError("Model not trained yet. Call train() first.")
        
        X_new = self.prepare_features(df_new)
        y_pred_proba = self.model.predict_proba(X_new)
        
        # Convert probabilities to binary predictions using threshold
        predictions_with_proba = []
        for i, row_proba in enumerate(y_pred_proba):
            row_predictions = []
            for j, estimator_proba in enumerate(row_proba):
                if len(estimator_proba) > 1:  # Binary classification case
                    prob_positive = estimator_proba[1]  # Probability of positive class
                else:
                    prob_positive = estimator_proba[0]  # Single class case
                
                if prob_positive >= threshold:
                    row_predictions.append((self.mlb.classes_[j], prob_positive))
            
            predictions_with_proba.append(row_predictions)
        
        return predictions_with_proba

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MultiLabelBinarizer, LabelEncoder, StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import hamming_loss, jaccard_score, accuracy_score
from sklearn.multioutput import MultiOutputClassifier
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from scipy.sparse import hstack, csr_matrix
import warnings
warnings.filterwarnings('ignore')

# For XGBoost and LightGBM
try:
    import xgboost as xgb
    import lightgbm as lgb
    GRADIENT_BOOSTING_AVAILABLE = True
except ImportError:
    GRADIENT_BOOSTING_AVAILABLE = False
    print("XGBoost and/or LightGBM not available. Install with: pip install xgboost lightgbm")

class FixedAdvancedFacilityPredictor:
    def __init__(self, model_type='random_forest', use_text_features=True, max_text_features=100):
        self.mlb = MultiLabelBinarizer()
        self.label_encoders = {}
        self.scaler = StandardScaler()
        self.text_vectorizer = TfidfVectorizer(
            max_features=max_text_features,
            ngram_range=(1, 2),
            lowercase=True,
            stop_words=None,
            min_df=1,  # Allow single occurrences
            dtype=np.float64  # Ensure consistent dtype
        ) if use_text_features else None
        self.model = None
        self.model_type = model_type
        self.feature_columns = []
        self.use_text_features = use_text_features
        
    def _get_base_model(self):
        """Get the base model based on model_type"""
        if self.model_type == 'xgboost' and GRADIENT_BOOSTING_AVAILABLE:
            return xgb.XGBClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                eval_metric='logloss',
                use_label_encoder=False,
                verbosity=0
            )
        elif self.model_type == 'lightgbm' and GRADIENT_BOOSTING_AVAILABLE:
            return lgb.LGBMClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42,
                verbose=-1,
                force_row_wise=True
            )
        elif self.model_type == 'gradient_boosting':
            return GradientBoostingClassifier(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=6,
                random_state=42
            )
        elif self.model_type == 'random_forest':
            return RandomForestClassifier(
                n_estimators=150,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=2,
                random_state=42,
                class_weight='balanced'
            )
        elif self.model_type == 'logistic_regression':
            return LogisticRegression(
                max_iter=1000,
                random_state=42,
                class_weight='balanced',
                C=1.0
            )
        elif self.model_type == 'svm':
            return SVC(
                kernel='rbf',
                probability=True,
                random_state=42,
                class_weight='balanced'
            )
        else:
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=15,
                random_state=42,
                class_weight='balanced'
            )
    
    def prepare_features(self, df):
        """Prepare features with proper data type handling"""
        df_features = df.copy()
        
        # Categorical features encoding
        categorical_features = ['Family', 'Variant', 'detail_alpha']
        
        for feature in categorical_features:
            if feature in df_features.columns:
                if feature not in self.label_encoders:
                    self.label_encoders[feature] = LabelEncoder()
                    df_features[f'{feature}_encoded'] = self.label_encoders[feature].fit_transform(
                        df_features[feature].fillna('Unknown').astype(str)
                    )
                else:
                    # Handle unseen categories
                    known_classes = set(self.label_encoders[feature].classes_)
                    values = df_features[feature].fillna('Unknown').astype(str)
                    
                    # Map known values, assign -1 to unknown
                    encoded_values = []
                    for val in values:
                        if val in known_classes:
                            encoded_values.append(self.label_encoders[feature].transform([val])[0])
                        else:
                            encoded_values.append(-1)
                    
                    df_features[f'{feature}_encoded'] = encoded_values
        
        # Define structured feature columns
        self.feature_columns = []
        
        # Numerical features
        numerical_features = ['Model', 'detail_numeric', 'detail_length']
        for feat in numerical_features:
            if feat in df_features.columns:
                self.feature_columns.append(feat)
        
        # Boolean features
        boolean_features = ['has_parentheses', 'has_GK', 'has_L', 'has_LM', 'has_W']
        for feat in boolean_features:
            if feat in df_features.columns:
                self.feature_columns.append(feat)
        
        # Encoded categorical features
        encoded_features = ['Family_encoded', 'Variant_encoded', 'detail_alpha_encoded']
        for feat in encoded_features:
            if feat in df_features.columns:
                self.feature_columns.append(feat)
        
        # Create structured feature matrix
        X_structured = df_features[self.feature_columns].copy()
        
        # Fill missing values and ensure correct dtypes
        X_structured = X_structured.fillna(0)
        
        # Convert boolean columns to int
        boolean_cols = [col for col in self.feature_columns if col.startswith('has_')]
        for col in boolean_cols:
            if col in X_structured.columns:
                X_structured[col] = X_structured[col].astype(int)
        
        # Convert all to float64 for consistency
        X_structured = X_structured.astype(np.float64)
        
        # Scale numerical features if this is training
        if not hasattr(self.scaler, 'scale_'):
            X_structured_scaled = self.scaler.fit_transform(X_structured)
        else:
            X_structured_scaled = self.scaler.transform(X_structured)
        
        # Convert to sparse matrix for consistency
        X_structured_sparse = csr_matrix(X_structured_scaled)
        
        # Add text features if enabled
        if self.use_text_features and 'item_description_clean' in df_features.columns:
            text_data = df_features['item_description_clean'].fillna('').astype(str)
            
            # Fit or transform text data
            if not hasattr(self.text_vectorizer, 'vocabulary_'):
                X_text = self.text_vectorizer.fit_transform(text_data)
            else:
                X_text = self.text_vectorizer.transform(text_data)
            
            # Ensure X_text is also float64
            X_text = X_text.astype(np.float64)
            
            # Combine sparse matrices
            X_combined = hstack([X_structured_sparse, X_text], format='csr')
            
            print(f"Structured features shape: {X_structured_sparse.shape}")
            print(f"Text features shape: {X_text.shape}")
            print(f"Combined features shape: {X_combined.shape}")
            
            return X_combined
        else:
            print(f"Structured features shape: {X_structured_sparse.shape}")
            return X_structured_sparse
    
    def prepare_targets(self, df):
        """Prepare multi-label targets"""
        if not hasattr(self.mlb, 'classes_'):
            y_binary = self.mlb.fit_transform(df['facility_list'])
        else:
            y_binary = self.mlb.transform(df['facility_list'])
        return y_binary
    
    def train(self, df):
        """Train the model with proper error handling"""
        print(f"Training {self.model_type} model...")
        
        try:
            # Prepare features and targets
            X = self.prepare_features(df)
            y = self.prepare_targets(df)
            
            print(f"Feature matrix shape: {X.shape}")
            print(f"Feature matrix dtype: {X.dtype}")
            print(f"Target matrix shape: {y.shape}")
            print(f"Available facilities: {list(self.mlb.classes_)}")
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Get base model
            base_model = self._get_base_model()
            
            # For tree-based models, convert sparse to dense if needed
            if self.model_type in ['xgboost', 'lightgbm']:
                # These can handle sparse matrices
                self.model = MultiOutputClassifier(base_model, n_jobs=1)  # Reduce parallelization to avoid conflicts
            else:
                # Convert to dense for other models if needed
                if hasattr(X_train, 'toarray'):
                    print("Converting sparse matrix to dense for compatibility...")
                    X_train = X_train.toarray()
                    X_test = X_test.toarray()
                
                self.model = MultiOutputClassifier(base_model, n_jobs=-1)
            
            # Train model
            print("Fitting model...")
            self.model.fit(X_train, y_train)
            
            # Make predictions
            print("Making predictions...")
            y_pred = self.model.predict(X_test)
            
            # Evaluate
            self._evaluate_model(y_test, y_pred)
            
            return X_train, X_test, y_train, y_test, y_pred
            
        except Exception as e:
            print(f"Detailed error in {self.model_type}: {str(e)}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            raise e
    
    def _evaluate_model(self, y_test, y_pred):
        """Evaluate model performance"""
        print(f"\n=== {self.model_type.upper()} Model Performance ===")
        
        try:
            hamming = hamming_loss(y_test, y_pred)
            jaccard = jaccard_score(y_test, y_pred, average='samples')
            exact_match = np.mean(np.all(y_test == y_pred, axis=1))
            
            print(f"Hamming Loss: {hamming:.4f}")
            print(f"Jaccard Score: {jaccard:.4f}")
            print(f"Exact Match Ratio: {exact_match:.4f}")
            
            print(f"\n=== Per-Facility Accuracy ===")
            for i, facility in enumerate(self.mlb.classes_):
                accuracy = accuracy_score(y_test[:, i], y_pred[:, i])
                support = np.sum(y_test[:, i])
                print(f"{facility}: {accuracy:.3f} (support: {support})")
                
        except Exception as e:
            print(f"Error in evaluation: {e}")
    
    def predict(self, df_new):
        """Predict facilities for new data"""
        if self.model is None:
            raise ValueError("Model not trained yet.")
        
        X_new = self.prepare_features(df_new)
        
        # Convert to dense if the training was done on dense arrays
        if hasattr(X_new, 'toarray') and self.model_type not in ['xgboost', 'lightgbm']:
            X_new = X_new.toarray()
        
        y_pred_binary = self.model.predict(X_new)
        return self.mlb.inverse_transform(y_pred_binary)

def compare_models_fixed(df):
    """Compare different models with better error handling"""
    models_to_test = ['random_forest', 'gradient_boosting']
    
    if GRADIENT_BOOSTING_AVAILABLE:
        models_to_test.extend(['xgboost', 'lightgbm'])
    
    results = {}
    
    for model_type in models_to_test:
        print(f"\n{'='*50}")
        print(f"Testing {model_type}")
        print('='*50)
        
        try:
            # Test with text features first
            predictor = FixedAdvancedFacilityPredictor(
                model_type=model_type, 
                use_text_features=True,
                max_text_features=50  # Reduce for stability
            )
            X_train, X_test, y_train, y_test, y_pred = predictor.train(df)
            
            # Calculate metrics
            hamming = hamming_loss(y_test, y_pred)
            jaccard = jaccard_score(y_test, y_pred, average='samples')
            
            results[model_type] = {
                'hamming_loss': hamming,
                'jaccard_score': jaccard,
                'predictor': predictor,
                'with_text': True
            }
            
        except Exception as e:
            print(f"Error with text features for {model_type}: {e}")
            
            # Try without text features as fallback
            try:
                print(f"Trying {model_type} without text features...")
                predictor_no_text = FixedAdvancedFacilityPredictor(
                    model_type=model_type, 
                    use_text_features=False
                )
                X_train, X_test, y_train, y_test, y_pred = predictor_no_text.train(df)
                
                hamming = hamming_loss(y_test, y_pred)
                jaccard = jaccard_score(y_test, y_pred, average='samples')
                
                results[model_type] = {
                    'hamming_loss': hamming,
                    'jaccard_score': jaccard,
                    'predictor': predictor_no_text,
                    'with_text': False
                }
                
            except Exception as e2:
                print(f"Error with {model_type} (no text): {e2}")
                results[model_type] = {'error': str(e2)}
    
    # Print comparison
    print(f"\n{'='*70}")
    print("MODEL COMPARISON RESULTS")
    print('='*70)
    print(f"{'Model':<20} {'Text Features':<15} {'Hamming Loss':<15} {'Jaccard Score':<15}")
    print('-'*70)
    
    for model, metrics in results.items():
        if 'error' not in metrics:
            text_feature_status = "Yes" if metrics.get('with_text', False) else "No"
            print(f"{model:<20} {text_feature_status:<15} {metrics['hamming_loss']:<15.4f} {metrics['jaccard_score']:<15.4f}")
        else:
            print(f"{model:<20} {'ERROR':<15} {metrics['error']}")
    
    # Find best model
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    if valid_results:
        best_model = min(valid_results.items(), key=lambda x: x[1]['hamming_loss'])
        print(f"\nBest Model: {best_model[0]} (Hamming Loss: {best_model[1]['hamming_loss']:.4f})")
        print(f"Uses text features: {'Yes' if best_model[1]['with_text'] else 'No'}")
        return best_model[1]['predictor']
    
    return None

# Simple test function
def test_single_model(df, model_type='random_forest'):
    """Test a single model for debugging"""
    print(f"Testing single model: {model_type}")
    print(f"DataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    
    # Check data types
    for col in df.columns:
        print(f"{col}: {df[col].dtype}, unique values: {df[col].nunique()}")
    
    try:
        predictor = FixedAdvancedFacilityPredictor(
            model_type=model_type, 
            use_text_features=True,
            max_text_features=20
        )
        
        results = predictor.train(df)
        print("SUCCESS!")
        return predictor
        
    except Exception as e:
        print(f"ERROR: {e}")
        import traceback
        traceback.print_exc()
        return None

# Usage:
best_predictor = compare_models_fixed(final)
# or for debugging:
# test_predictor = test_single_model(your_df, 'random_forest')

import pandas as pd
import numpy as np
import pickle
from sklearn.metrics import hamming_loss, jaccard_score
import warnings
warnings.filterwarnings('ignore')

class ProductionFacilityPredictor:
    """
    Production-ready version of your best performing model
    """
    def __init__(self, best_predictor):
        self.predictor = best_predictor
        
    def save_model(self, filepath):
        """Save the trained model"""
        with open(filepath, 'wb') as f:
            pickle.dump(self.predictor, f)
        print(f"Model saved to {filepath}")
    
    @classmethod
    def load_model(cls, filepath):
        """Load a saved model"""
        with open(filepath, 'rb') as f:
            predictor = pickle.load(f)
        return cls(predictor)
    
    def predict_facilities(self, item_descriptions):
        """
        Predict facilities for a list of item descriptions
        
        Args:
            item_descriptions: List of item descriptions or single string
            
        Returns:
            List of facility predictions
        """
        if isinstance(item_descriptions, str):
            item_descriptions = [item_descriptions]
        
        # Create dataframe in the expected format
        df_new = pd.DataFrame({
            'item_description': item_descriptions
        })
        
        # Preprocess the same way as training data
        df_processed = self.preprocess_new_data(df_new)
        
        # Make predictions
        predictions = self.predictor.predict(df_processed)
        
        return predictions
    
    def predict_with_confidence(self, item_descriptions, threshold=0.5):
        """
        Predict facilities with confidence scores
        """
        if isinstance(item_descriptions, str):
            item_descriptions = [item_descriptions]
        
        df_new = pd.DataFrame({
            'item_description': item_descriptions
        })
        
        df_processed = self.preprocess_new_data(df_new)
        
        # Get probability predictions if available
        try:
            X_new = self.predictor.prepare_features(df_processed)
            if hasattr(X_new, 'toarray'):
                X_new = X_new.toarray()
            
            # Get probabilities from each estimator
            probabilities = []
            for estimator in self.predictor.model.estimators_:
                if hasattr(estimator, 'predict_proba'):
                    proba = estimator.predict_proba(X_new)
                    # Extract probability of positive class
                    if len(proba) > 1:
                        probabilities.append(proba[:, 1])
                    else:
                        probabilities.append(proba[:, 0])
            
            # Combine probabilities
            if probabilities:
                combined_proba = np.column_stack(probabilities)
                
                results = []
                for i, desc in enumerate(item_descriptions):
                    item_results = []
                    for j, facility in enumerate(self.predictor.mlb.classes_):
                        prob = combined_proba[i, j]
                        if prob >= threshold:
                            item_results.append((facility, prob))
                    
                    # Sort by confidence
                    item_results.sort(key=lambda x: x[1], reverse=True)
                    results.append({
                        'item_description': desc,
                        'predicted_facilities': item_results
                    })
                
                return results
        except:
            # Fallback to regular prediction
            pass
        
        # Regular predictions
        predictions = self.predict_facilities(item_descriptions)
        return [{'item_description': desc, 'predicted_facilities': list(pred)} 
                for desc, pred in zip(item_descriptions, predictions)]
    
    def preprocess_new_data(self, df):
        """
        Preprocess new data the same way as training data
        """
        df_processed = df.copy()
        
        # Clean item descriptions
        df_processed['item_description_clean'] = df_processed['item_description'].str.replace(r'\s+RAIL$', '', regex=True)
        
        # Split description
        df_processed[['FMV', 'detail']] = df_processed['item_description_clean'].str.split('-', n=1, expand=True)
        df_processed['FMV'] = df_processed['FMV'].fillna(df_processed['item_description_clean'])
        df_processed['detail'] = df_processed['detail'].fillna('')
        
        # Extract features
        df_processed['Family'] = df_processed['FMV'].str.extract(r'^([A-Za-z]+)')
        df_processed['Model'] = df_processed['FMV'].str.extract(r'([0-9]+)').astype('Int64')
        df_processed['Variant'] = df_processed['FMV'].str.extract(r'[0-9]+([A-Za-z]+)')
        df_processed['detail_numeric'] = df_processed['detail'].str.extract(r'(\d+)').astype(float)
        df_processed['detail_alpha'] = df_processed['detail'].str.extract(r'([A-Za-z]+)')
        df_processed['detail_length'] = df_processed['detail'].str.len()
        df_processed['has_parentheses'] = df_processed['detail'].str.contains(r'\(.*\)', na=False)
        df_processed['has_GK'] = df_processed['detail'].str.contains('GK', na=False)
        df_processed['has_L'] = df_processed['detail'].str.contains('L', na=False)
        df_processed['has_LM'] = df_processed['detail'].str.contains('LM', na=False)
        df_processed['has_W'] = df_processed['FMV'].str.contains('W', na=False)
        
        # Clean up
        df_processed = df_processed.drop(['item_description', 'FMV', 'detail'], axis=1, errors='ignore')
        
        return df_processed
    
    def batch_predict(self, csv_filepath, output_filepath=None):
        """
        Predict facilities for a CSV file of item descriptions
        """
        # Read CSV
        df = pd.read_csv(csv_filepath)
        
        if 'item_description' not in df.columns:
            raise ValueError("CSV must have 'item_description' column")
        
        # Make predictions
        predictions = self.predict_facilities(df['item_description'].tolist())
        
        # Add predictions to dataframe
        df['predicted_facilities'] = [list(pred) for pred in predictions]
        df['predicted_facilities_str'] = df['predicted_facilities'].apply(lambda x: ', '.join(x))
        
        # Save if output path provided
        if output_filepath:
            df.to_csv(output_filepath, index=False)
            print(f"Predictions saved to {output_filepath}")
        
        return df
    
    def evaluate_on_test_data(self, test_df):
        """
        Evaluate model performance on test data
        """
        # Preprocess test data
        df_processed = self.preprocess_new_data(test_df)
        
        # Make predictions
        X_test = self.predictor.prepare_features(df_processed)
        if hasattr(X_test, 'toarray'):
            X_test = X_test.toarray()
        
        y_pred = self.predictor.model.predict(X_test)
        
        # If we have actual facilities for comparison
        if 'facility_ids' in test_df.columns:
            # Parse actual facilities
            actual_facilities = test_df['facility_ids'].str.split(', ').tolist()
            y_actual = self.predictor.mlb.transform(actual_facilities)
            
            # Calculate metrics
            hamming = hamming_loss(y_actual, y_pred)
            jaccard = jaccard_score(y_actual, y_pred, average='samples')
            exact_match = np.mean(np.all(y_actual == y_pred, axis=1))
            
            print(f"Test Set Performance:")
            print(f"Hamming Loss: {hamming:.4f}")
            print(f"Jaccard Score: {jaccard:.4f}")
            print(f"Exact Match Ratio: {exact_match:.4f}")
            
            return {
                'hamming_loss': hamming,
                'jaccard_score': jaccard,
                'exact_match_ratio': exact_match
            }
        
        return None

# Example usage and testing
def demonstrate_production_model(best_predictor):
    """
    Demonstrate how to use the production model
    """
    print("=== Production Model Demo ===")
    
    # Create production model
    prod_model = ProductionFacilityPredictor(best_predictor)
    
    # Test predictions
    test_items = [
        "SRS12W-390LM RAIL",
        "SRS15-150LM RAIL", 
        "SHW21-580L(GK) RAIL",
        "SR35-3000L(GK) RAIL"
    ]
    
    print("Testing predictions:")
    predictions = prod_model.predict_facilities(test_items)
    
    for item, pred in zip(test_items, predictions):
        print(f"{item} → {list(pred)}")
    
    print("\nTesting with confidence scores:")
    confidence_predictions = prod_model.predict_with_confidence(test_items[:2])
    
    for result in confidence_predictions:
        print(f"\n{result['item_description']}:")
        for facility, conf in result['predicted_facilities']:
            print(f"  {facility}: {conf:.3f}")
    
    # Save model
    print(f"\nSaving model...")
    prod_model.save_model('facility_predictor_model.pkl')
    
    return prod_model

# Usage after training:
prod_model = demonstrate_production_model(best_predictor)

import re

product_families = ["HSR", "SHS", "SR", "HDR", "SRS-W", "HRW", "SHW", "TY", "SRS", "GL"]
product_models = ["65", "55", "25","27", "35", "15", "20", "30", "45", "17", "21", "12", "9"]
product_variants = [None, "A", "B", "C", "CA", "CAN", "CR", "CRN", "G", "HA", "HV", "LA", "LB", "LC", "LE", "LR", "LV", "M", "N", "R", "SB", "TB", "V", "W", "XC", "XLC", "XLR", "XR"]

# Sort families by length (descending) to match longest first
sorted_families = sorted(product_families, key=len, reverse=True)
sorted_variants = sorted([v for v in product_variants if v is not None], key=len, reverse=True)

def extract_fmv(fmv_str):
    family = model = variant = None

    for fam in sorted_families:
        if fmv_str.startswith(fam):
            family = fam
            remaining = fmv_str[len(fam):]
            break
    else:
        return '', 0, None  # Family not found

    # Match model from the remaining string
    model_match = re.match(r'^(\d+)', remaining)
    if model_match:
        model = model_match.group(1)
        remaining = remaining[len(model):]
    else:
        model = '0'

    # Check if remaining starts with a known variant
    for var in sorted_variants:
        if remaining.startswith(var):
            variant = var
            break

    return family, int(model), variant


import sys, os, pickle, warnings, re
import pandas as pd
warnings.filterwarnings('ignore')

def predict_facilities(description, model_path='facility_predictor_model.pkl'):
    try:
        # Suppress stdout
        class DummyFile: 
            def write(self, x): pass
        old_stdout = sys.stdout
        sys.stdout = DummyFile()
        
        # Load model
        with open(model_path, 'rb') as f:
            predictor = pickle.load(f)
        
        # Restore stdout
        sys.stdout = old_stdout

        # Clean description
        description_clean = str(description).strip()
        if description_clean.upper().endswith(' RAIL'):
            description_clean = description_clean[:-5].strip()
        fmv_part, detail_part = (description_clean.split('-', 1) + [''])[:2]

        family, model, variant = extract_fmv(fmv_part)


        detail_numeric_match = re.search(r'(\d+)', detail_part)
        detail_numeric = float(detail_numeric_match.group(1)) if detail_numeric_match else 0

        detail_alpha_match = re.search(r'([A-Za-z]+)', detail_part)
        detail_alpha = detail_alpha_match.group(1) if detail_alpha_match else None

        detail_length = len(detail_part)

        df_input = pd.DataFrame({
            'item_description_clean': [description_clean],
            'Family': [family],
            'Model': [model],
            'Variant': [variant],
            'detail_numeric': [detail_numeric],
            'detail_alpha': [detail_alpha],
            'detail_length': [detail_length],
            'has_parentheses': ['(' in detail_part and ')' in detail_part],
            'has_GK': ['GK' in detail_part],
            'has_L': ['L' in detail_part],
            'has_LM': ['LM' in detail_part],
            'has_W': ['W' in fmv_part]
        })

        # Suppress stdout again during prediction
        sys.stdout = DummyFile()
        predicted_facilities = predictor.predict(df_input)[0]
        sys.stdout = old_stdout

        return {
            'predicted_facilities': list(predicted_facilities),
            'product_family': family,
            'product_model': model,
            'product_variant': variant,
            'product_length': detail_numeric
        }

    except:
        return {
            'predicted_facilities': [],
            'product_family': '',
            'product_model': 0,
            'product_variant': None,
            'product_length': 0
        }


facilities = predict_facilities("SHW21-340L (GK) RAIL")

display(facilities)  # ['D0010', 'D0020', 'F0010']