// material-specs.js - Material Specifications Management

// Global variables
let materialSpecs = [];
let modifiedSpecs = new Set();
let newSpecCounter = 0;

// Open the material specs modal
function openSpecsModal() {
    document.getElementById('material-specs-modal').classList.remove('hidden');
    loadMaterialSpecs();
}

// Close the material specs modal
function closeSpecsModal() {
    if (modifiedSpecs.size > 0) {
        if (confirm('You have unsaved changes. Are you sure you want to close without saving?')) {
            document.getElementById('material-specs-modal').classList.add('hidden');
            modifiedSpecs.clear();
        }
    } else {
        document.getElementById('material-specs-modal').classList.add('hidden');
    }
}

// Load material specs from the backend
async function loadMaterialSpecs() {
    try {
        document.getElementById('specs-table-body').innerHTML = `
            <tr>
                <td colspan="8" class="py-4 text-center text-gray-500">
                    <div class="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                    Loading material specifications...
                </td>
            </tr>
        `;
        
        document.getElementById('specs-error').classList.add('hidden');
        
        const response = await fetch('/auto-cut/material-specs');
        const data = await response.json();
        
        if (data.status === 'success') {
            materialSpecs = data.material_specs;
            modifiedSpecs.clear();
            renderSpecsTable();
        } else {
            throw new Error(data.message || 'Failed to load material specifications');
        }
    } catch (error) {
        console.error('Error loading material specs:', error);
        showSpecsError(`Error loading specifications: ${error.message}`);
        document.getElementById('specs-table-body').innerHTML = `
            <tr>
                <td colspan="8" class="py-4 text-center text-red-500">
                    Failed to load data. Please try again.
                </td>
            </tr>
        `;
    }
}

// Show error message in the specs modal
function showSpecsError(message) {
    const errorEl = document.getElementById('specs-error');
    errorEl.textContent = message;
    errorEl.classList.remove('hidden');
}

// Hide error message in the specs modal
function hideSpecsError() {
    document.getElementById('specs-error').classList.add('hidden');
}

// Render the material specs table
function renderSpecsTable() {
    const tableBody = document.getElementById('specs-table-body');
    tableBody.innerHTML = '';
    
    if (materialSpecs.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="py-4 text-center text-gray-500">
                    No specifications found. Click "Add New Spec" to create one.
                </td>
            </tr>
        `;
        return;
    }
    
    materialSpecs.forEach(spec => {
        const row = document.createElement('tr');
        row.dataset.id = spec.id;
        row.className = modifiedSpecs.has(spec.id) ? 'modified' : '';
        
        row.innerHTML = `
            <td class="py-2 px-3 border-b">
                <input type="text" class="editable-input" value="${spec.rail_desc}" data-field="rail_desc" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <input type="number" class="editable-input" value="${spec.length}" data-field="length" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <input type="number" class="editable-input" value="${spec.pitch}" data-field="pitch" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <input type="number" class="editable-input" value="${spec.g}" data-field="g" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <input type="number" class="editable-input" value="${spec.margin}" data-field="margin" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <input type="number" class="editable-input" value="${spec.cut_time}" data-field="cut_time" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <input type="text" class="editable-input" value="${spec.type}" data-field="type" onchange="markAsModified(${spec.id})">
            </td>
            <td class="py-2 px-3 border-b">
                <div class="flex space-x-2">
                    <button onclick="saveSpec(${spec.id})" class="text-blue-500 hover:text-blue-700" title="Save">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </button>
                    <button onclick="deleteSpec(${spec.id})" class="text-red-500 hover:text-red-700" title="Delete">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                </div>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
}

// Mark a spec as modified
function markAsModified(id) {
    modifiedSpecs.add(id);
    const row = document.querySelector(`tr[data-id="${id}"]`);
    if (row) {
        row.className = 'modified';
    }
}

// Add a new material spec row
function addNewSpec() {
    newSpecCounter--;
    const newSpec = {
        id: newSpecCounter, // Temporary negative ID for new specs
        rail_desc: 'New Rail',
        length: 9000,
        pitch: 60,
        g: 30,
        margin: 7,
        cut_time: 40,
        type: 'NEW'
    };
    
    materialSpecs.push(newSpec);
    modifiedSpecs.add(newSpec.id);
    renderSpecsTable();
    
    // Scroll to the bottom of the table
    const tableContainer = document.querySelector('.overflow-x-auto');
    tableContainer.scrollTop = tableContainer.scrollHeight;
}

// Save a single spec
async function saveSpec(id) {
    if (!modifiedSpecs.has(id)) {
        return; // Not modified, nothing to save
    }
    
    try {
        hideSpecsError();
        const row = document.querySelector(`tr[data-id="${id}"]`);
        
        if (!row) {
            throw new Error('Row not found');
        }
        
        const spec = {
            rail_desc: row.querySelector('[data-field="rail_desc"]').value,
            length: parseFloat(row.querySelector('[data-field="length"]').value),
            pitch: parseFloat(row.querySelector('[data-field="pitch"]').value),
            g: parseFloat(row.querySelector('[data-field="g"]').value),
            margin: parseFloat(row.querySelector('[data-field="margin"]').value),
            cut_time: parseFloat(row.querySelector('[data-field="cut_time"]').value),
            type: row.querySelector('[data-field="type"]').value
        };
        
        // Validation
        if (!spec.rail_desc || !spec.type) {
            throw new Error('Rail description and type are required');
        }
        
        if (isNaN(spec.length) || isNaN(spec.pitch) || isNaN(spec.g) || 
            isNaN(spec.margin) || isNaN(spec.cut_time)) {
            throw new Error('All numeric fields must be valid numbers');
        }
        
        let response;
        let url;
        let method;
        
        if (id < 0) {
            // New spec
            url = '/auto-cut/material-specs';
            method = 'POST';
        } else {
            // Existing spec
            url = `/auto-cut/material-specs/${id}`;
            method = 'PUT';
        }
        
        response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(spec)
        });
        
        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error(data.message || 'Failed to save');
        }
        
        // Update was successful
        modifiedSpecs.delete(id);
        row.className = '';
        
        // If it was a new spec, reload all specs to get the real ID
        if (id < 0) {
            loadMaterialSpecs();
        }
        
    } catch (error) {
        console.error('Error saving spec:', error);
        showSpecsError(`Error: ${error.message}`);
    }
}

// Save all modified specs
async function saveAllChanges() {
    if (modifiedSpecs.size === 0) {
        alert('No changes to save');
        return;
    }
    
    try {
        hideSpecsError();
        const promises = [];
        
        for (const id of modifiedSpecs) {
            const row = document.querySelector(`tr[data-id="${id}"]`);
            
            if (!row) continue;
            
            const spec = {
                rail_desc: row.querySelector('[data-field="rail_desc"]').value,
                length: parseFloat(row.querySelector('[data-field="length"]').value),
                pitch: parseFloat(row.querySelector('[data-field="pitch"]').value),
                g: parseFloat(row.querySelector('[data-field="g"]').value),
                margin: parseFloat(row.querySelector('[data-field="margin"]').value),
                cut_time: parseFloat(row.querySelector('[data-field="cut_time"]').value),
                type: row.querySelector('[data-field="type"]').value
            };
            
            // Validation
            if (!spec.rail_desc || !spec.type) {
                throw new Error(`Rail description and type are required (Row ID: ${id})`);
            }
            
            let url;
            let method;
            
            if (id < 0) {
                // New spec
                url = '/auto-cut/material-specs';
                method = 'POST';
            } else {
                // Existing spec
                url = `/auto-cut/material-specs/${id}`;
                method = 'PUT';
            }
            
            promises.push(fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(spec)
            }));
        }
        
        // Wait for all saves to complete
        await Promise.all(promises);
        
        // Reload to get the latest data with correct IDs
        loadMaterialSpecs();
        
        alert('All changes saved successfully');
        
    } catch (error) {
        console.error('Error saving all specs:', error);
        showSpecsError(`Error: ${error.message}`);
    }
}

// Delete a spec
async function deleteSpec(id) {
    if (!confirm('Are you sure you want to delete this specification?')) {
        return;
    }
    
    try {
        hideSpecsError();
        
        // If it's a new spec that hasn't been saved yet
        if (id < 0) {
            materialSpecs = materialSpecs.filter(spec => spec.id !== id);
            modifiedSpecs.delete(id);
            renderSpecsTable();
            return;
        }
        
        // Otherwise, delete from the server
        const response = await fetch(`/auto-cut/material-specs/${id}`, {
            method: 'DELETE'
        });
        
        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error(data.message || 'Failed to delete');
        }
        
        // Delete was successful
        materialSpecs = materialSpecs.filter(spec => spec.id !== id);
        modifiedSpecs.delete(id);
        renderSpecsTable();
        
    } catch (error) {
        console.error('Error deleting spec:', error);
        showSpecsError(`Error: ${error.message}`);
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for ESC key to close modal
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeSpecsModal();
        }
    });
});