#!/usr/bin/python
import psycopg2
from configparser import ConfigPars<PERSON>
from psycopg2.extras import RealDictCursor

def config(db, section="postgresql"):
    # create a parser
    parser = ConfigParser()
    # read config file
    if db == "postgres":
        parser.read("./etc/postgres.ini")
    else:
        parser.read("./etc/ignition.ini")
    # get section, default to postgresql
    db = {}
    if parser.has_section(section):
        params = parser.items(section)
        for param in params:
            db[param[0]] = param[1]
    else:
        raise Exception("Section {0} not found in the {1} file".format(section, db))
    return db

def update_ignition(sql):
    conn = None
    try:
        params = config(db="ignition")
        conn = psycopg2.connect(**params)
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute(sql)
        conn.commit()
        return "success"
    except (Exception, psycopg2.DatabaseError) as error:
        print(error)
    finally:
        if conn is not None:
            conn.close()


def call_db_json(sql):#used
    conn = None
    try:
        params = config(db="postgres")
        conn = psycopg2.connect(**params)
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute(sql)
        row = cur.fetchall()
        cur.close()
        return row
    except (Exception, psycopg2.DatabaseError) as error:
        print(error)
    finally:
        if conn is not None:
            conn.close()


def call_ignition(sql):#used
    conn = None
    try:
        params = config(db="ignition")
        conn = psycopg2.connect(**params)
        cur = conn.cursor(cursor_factory=RealDictCursor)
        cur.execute(sql)
        row = cur.fetchall()
        cur.close()
        return row
    except (Exception, psycopg2.DatabaseError) as error:
        print(error)
    finally:
        if conn is not None:
            conn.close()

def update_postgres(query, params):
    conn = None
    try:
        params_config = config(db="postgres")  # Renamed to avoid conflict
        conn = psycopg2.connect(**params_config)
        cur = conn.cursor()
        cur.execute(query, params)  # Use parameterized query
        conn.commit()
        result = cur.fetchone()[0]  # Get the RETURNING id value
        return result
    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Database error: {error}")
        if conn:
            conn.rollback()
        raise  # Re-raise the exception for FastAPI to handle
    finally:
        if conn is not None:
            conn.close()


