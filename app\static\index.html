<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rail Cutting Arrangement</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container-fluid px-4 w-full">
        <!-- Filter Controls -->
        <div class="filter-container bg-white p-4 rounded shadow mb-6">
            <h2 class="text-xl font-semibold mb-4">Rail Arrangement Filter</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- MO Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Filter by MO</label>
                    <div class="flex">
                        <input type="text" id="mo-input" class="flex-1 border rounded p-2 mr-2" placeholder="Enter MO number">
                        <button onclick="addMoFilter()" class="bg-blue-500 text-white px-3 py-2 rounded">Add</button>
                    </div>
                    <div id="mo-filters" class="mt-2 min-h-8"></div>
                </div>
                
                <!-- HK Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Filter by HK</label>
                    <div class="flex">
                        <input type="text" id="hk-input" class="flex-1 border rounded p-2 mr-2" placeholder="Enter HK number">
                        <button onclick="addHkFilter()" class="bg-blue-500 text-white px-3 py-2 rounded">Add</button>
                    </div>
                    <div id="hk-filters" class="mt-2 min-h-8"></div>
                </div>
            </div>
            
            <div class="mt-4 flex justify-between">
                <div>
                    <button id="show-specs-btn" onclick="openSpecsModal()" class="bg-blue-500 text-white px-4 py-2 rounded mr-2">
                        Material Specs
                    </button>
                    <button onclick="clearFilters()" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2">Clear Filters</button>
                    <button onclick="restoreAllFilters()" class="bg-blue-400 text-white px-4 py-2 rounded">Restore All</button>
                </div>
                <button onclick="fetchArrangement()" class="bg-green-600 text-white px-6 py-2 rounded font-medium">Generate Arrangement</button>
            </div>
        </div>
        
        <!-- Arrangement Results -->
        <div id="arrangement-container" class="grid grid-cols-1 gap-4">
            <div class="col-span-1 text-center text-gray-500 py-10">
                Click "Generate Arrangement" to view optimized rail cutting layouts
            </div>
        </div>
    </div>

    <script src="app.js"></script>
    <script src="material-specs.js"></script>
    <!-- Material Specs Management Modal -->
<div id="material-specs-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden overflow-auto">
    <div class="bg-white w-11/12 max-w-7xl mx-auto my-10 rounded-lg shadow">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-4 border-b">
            <h2 class="text-xl font-semibold">Material Specifications</h2>
            <button onclick="closeSpecsModal()" class="text-gray-500 hover:text-gray-800 text-2xl font-bold">&times;</button>
        </div>
        
        <!-- Modal Body -->
        <div class="p-6">
            <!-- Controls -->
            <div class="flex justify-end space-x-3 mb-4">
                <button onclick="addNewSpec()" class="bg-blue-500 text-white px-4 py-2 rounded text-sm">
                    Add New Spec
                </button>
                <button onclick="saveAllChanges()" class="bg-green-600 text-white px-4 py-2 rounded text-sm">
                    Save All Changes
                </button>
            </div>
            
            <!-- Table Container -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Rail Description</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Length (mm)</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Pitch</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">G</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Margin</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Cut Time</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Type</th>
                            <th class="py-2 px-3 border-b text-left text-sm font-medium text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="specs-table-body">
                        <!-- Rows will be added dynamically -->
                    </tbody>
                </table>
            </div>
            
            <!-- Error message area -->
            <div id="specs-error" class="mt-4 text-red-600 hidden"></div>
        </div>
    </div>
</div>
</body>
</html>