import warnings
import os
import json
from pathlib import Path
from fastapi import <PERSON><PERSON><PERSON>, Response, status, HTTPException, Request, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel
from typing import List, Optional, Literal
from one import add_to_cart, check_and_clean_carts, periodic_cleanup, create_auto_arrangement, get_mo_details
import asyncio
import logging
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from db import call_db_json, update_postgres

# Suppress FutureWarning messages to keep logs cleaner
warnings.simplefilter(action="ignore", category=FutureWarning)

# Setup logging with INFO level for application events
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI application
app = FastAPI()

@app.on_event("startup")
async def startup_event():
    """
    Startup event handler that runs when the FastAPI application starts.
    
    This function creates a background task that periodically cleans up
    invalid cart entries from the database every 5 minutes.
    """
    # Start the periodic cleanup task as a background task
    asyncio.create_task(periodic_cleanup())

# Determine the base path for API routes based on environment
if os.getenv("MODE") == "development":
    # No base path prefix in development mode
    basePath = ""
else:
    # In production, read base path from config file
    config_path = Path("./config.json")
    with open(config_path, "r") as f:
        config = json.load(f)
        basePath = config["basePath"]

# Configure CORS (Cross-Origin Resource Sharing) to allow requests from any origin
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define Pydantic models for request/response validation and documentation

class HealthCheck(BaseModel):
    """Model for health check response"""
    status: str = "OK"

class CartItem(BaseModel):
    """
    Model for a cart item
    
    Represents a single item to be added to the cart with its associated
    manufacturing orders and hardware kits.
    """
    batch_id: str
    total_length: float
    item_description: str
    total_quantity: int
    mos: str  # Comma-separated list of manufacturing order numbers
    hks: str  # Comma-separated list of hardware kit identifiers
    mode: Literal['auto', 'manual']  # Mode of arrangement

class CartBatch(BaseModel):
    """Model for a batch of cart items to be processed together"""
    items: List[CartItem]

class ArrangementRequest(BaseModel):
    """
    Model for an auto arrangement request
    
    Allows filtering the arrangement process to specific MOs or HKs.
    """
    specific_mos: Optional[List[str]] = None  # Optional list of specific MO numbers
    specific_hks: Optional[List[str]] = None  # Optional list of specific HK identifiers

# API Routes
class MaterialSpec(BaseModel):
    """Model for material specification"""
    rail_desc: str
    length: float
    pitch: float
    g: float
    margin: float
    cut_time: float
    type: str


@app.get(
    "/health",
    tags=["healthcheck"],
    summary="Perform a Health Check",
    response_description="Return HTTP Status Code 200 (OK)",
    status_code=status.HTTP_200_OK,
    response_model=HealthCheck,
)
def get_health() -> HealthCheck:
    """
    Health check endpoint to verify API is running
    
    Returns a simple OK status response.
    """
    return HealthCheck(status="OK")

@app.get(basePath + "/")
async def root():
    """
    Root endpoint for the API
    
    Returns a welcome message. This serves as a simple test to verify
    that the API is reachable and the base path is configured correctly.
    """
    return {"message": "Welcome to the project status page"}

@app.post(basePath + "/cart")
async def add_cart_batch(batch: CartBatch):
    """
    Endpoint to add a batch of items to the cart
    
    Processes multiple cart items in a single batch operation,
    adding each to the database.
    
    Parameters:
        batch (CartBatch): A batch of cart items to process
        
    Returns:
        dict: Status and results of the batch processing
        
    Raises:
        HTTPException: If batch processing fails
    """
    try:
        results = []
        # Process each item in the batch
        for item in batch.items:
            # Add this item to the cart database
            result = add_to_cart(
                batch_id=item.batch_id,
                total_length=item.total_length,
                item_description=item.item_description,
                total_quantity=item.total_quantity,
                mos=item.mos,
                hks=item.hks,
                mode=item.mode
            )
            results.append(result)
        
        # Return success response with results
        return {"status": "Batch processed", "results": results}
    except Exception as e:
        # If any error occurs, return a 500 error with details
        raise HTTPException(
            status_code=500,
            detail=f"Batch processing failed: {str(e)}"
        )

@app.post(basePath + "/cart-cleanup")
async def cleanup_carts():
    """
    Endpoint to manually trigger cart cleanup
    
    This is in addition to the automatic periodic cleanup.
    Checks all carts and removes invalid ones.
    
    Returns:
        dict: Results of the cleanup operation
        
    Raises:
        HTTPException: If cleanup fails
    """
    try:
        # Run the cleanup function
        result = check_and_clean_carts()
        return result
    except Exception as e:
        # If cleanup fails, return a 500 error with details
        raise HTTPException(
            status_code=500,
            detail=f"Cart cleanup failed: {str(e)}"
        )

@app.post(basePath + "/arrangement-auto")
async def start_auto_arrangement(request: ArrangementRequest = Body({})):
    """
    Endpoint to generate optimized rail cutting arrangements
    
    This is the main endpoint for the optimization algorithm. It processes
    cart data and returns an optimized arrangement of cuts.
    
    Parameters:
        request (ArrangementRequest): Optional filter criteria for MOs and HKs
        
    Returns:
        JSONResponse: Results of the arrangement optimization
    """
    try:
        logger.info("Starting auto arrangement process")
        logger.info(f"Received specific_mos: {request.specific_mos}")
        logger.info(f"Received specific_hks: {request.specific_hks}")
        
        # Call the optimization function with optional filters
        result = create_auto_arrangement(
            specific_mos=request.specific_mos,
            specific_hks=request.specific_hks
        )
        
        logger.info(f"Auto arrangement completed successfully with {len(result['results'])} results")
        return JSONResponse(content={
            "status": "success",
            "details": result
        })
    except Exception as e:
        # Log the error and return a 500 response
        logger.error(f"Auto arrangement failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Auto arrangement failed: {str(e)}"
            }
        )

@app.get(basePath + "/available-items")
async def get_available_items():
    """
    Endpoint to get all available MOs and HKs from carts
    
    This provides a list of all unique MO and HK identifiers that
    are currently in auto-mode carts. Useful for UI dropdowns and
    filtering options.
    
    Returns:
        JSONResponse: Lists of available MOs and HKs
    """
    try:
        # Get all carts with mode 'auto'
        query = '''
        SELECT mos, hks
        FROM public.carts
        WHERE mode = 'auto'
        '''
        cart_data = call_db_json(query)
        
        all_mos = []
        all_hks = []
        
        # Extract all MOs and HKs from cart data
        for cart in cart_data:
            if cart.get('mos'):
                # Split and strip MO numbers
                mos_list = [mo.strip() for mo in cart['mos'].split(',')]
                all_mos.extend(mos_list)
            
            if cart.get('hks'):
                # Split and strip HK identifiers
                hks_list = [hk.strip() for hk in cart['hks'].split(',')]
                all_hks.extend(hks_list)
        
        # Remove duplicates using set conversion
        unique_mos = list(set(all_mos))
        unique_hks = list(set(all_hks))
        
        # Sort for nicer display in UI
        unique_mos.sort()
        unique_hks.sort()
        
        return JSONResponse(content={
            "status": "success",
            "mos": unique_mos,
            "hks": unique_hks
        })
    except Exception as e:
        # Log the error and return a 500 response
        logger.error(f"Failed to fetch available items: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Failed to fetch available items: {str(e)}"
            }
        )


@app.get(basePath + "/material-specs")
async def get_material_specs():
    """
    Endpoint to get all material specifications
    
    Returns:
        JSONResponse: List of all material specifications
    """
    try:
        query = '''
        SELECT id, rail_desc, length, pitch, g, margin, cut_time, type
        FROM public.material_specs
        ORDER BY type, rail_desc
        '''
        results = call_db_json(query)
        
        return JSONResponse(content={
            "status": "success",
            "material_specs": results
        })
    except Exception as e:
        logger.error(f"Failed to fetch material specs: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Failed to fetch material specs: {str(e)}"
            }
        )

@app.post(basePath + "/material-specs")
async def add_material_spec(spec: MaterialSpec):
    """
    Endpoint to add a new material specification
    
    Parameters:
        spec (MaterialSpec): Material specification to add
        
    Returns:
        JSONResponse: Result of the operation
    """
    try:
        query = '''
        INSERT INTO public.material_specs
        (rail_desc, length, pitch, g, margin, cut_time, type)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        RETURNING id
        '''
        
        params = (
            spec.rail_desc,
            spec.length,
            spec.pitch,
            spec.g,
            spec.margin,
            spec.cut_time,
            spec.type
        )
        
        result = update_postgres(query, params)
        
        return JSONResponse(content={
            "status": "success",
            "message": "Material specification added successfully",
            "id": result
        })
    except Exception as e:
        logger.error(f"Failed to add material spec: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Failed to add material spec: {str(e)}"
            }
        )

@app.put(basePath + "/material-specs/{spec_id}")
async def update_material_spec(spec_id: int, spec: MaterialSpec):
    """
    Endpoint to update an existing material specification
    """
    try:
        # Add RETURNING id to the query so there's a result to fetch
        query = '''
        UPDATE public.material_specs
        SET rail_desc = %s, length = %s, pitch = %s, g = %s, 
            margin = %s, cut_time = %s, type = %s
        WHERE id = %s
        RETURNING id
        '''
        
        params = (
            spec.rail_desc,  # Make sure this matches your column name (rail_type or rail_desc)
            spec.length,
            spec.pitch,
            spec.g,
            spec.margin,
            spec.cut_time,
            spec.type,
            spec_id
        )
        
        result = update_postgres(query, params)
        
        return JSONResponse(content={
            "status": "success",
            "message": "Material specification updated successfully",
            "id": result
        })
    except Exception as e:
        logger.error(f"Failed to update material spec: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Failed to update material spec: {str(e)}"
            }
        )

@app.delete(basePath + "/material-specs/{spec_id}")
async def delete_material_spec(spec_id: int):
    """
    Endpoint to delete a material specification
    
    Parameters:
        spec_id (int): ID of the material specification to delete
        
    Returns:
        JSONResponse: Result of the operation
    """
    try:
        query = '''
        DELETE FROM public.material_specs
        WHERE id = %s
        '''
        
        update_postgres(query, (spec_id,))
        
        return JSONResponse(content={
            "status": "success",
            "message": "Material specification deleted successfully"
        })
    except Exception as e:
        logger.error(f"Failed to delete material spec: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": f"Failed to delete material spec: {str(e)}"
            }
        )


# Add this in main.py without any basePath
@app.get(basePath + "/mo-details/{mo_number}")
async def get_mo_detail_alternate(mo_number: str):
    """Alternate endpoint without basePath"""
    try:
        mo_details = get_mo_details(mo_number)
        # Rest of code same as before
        if mo_details:
            return JSONResponse(content={"status": "success", "rail_type": mo_details.get('rail_type', 'Unknown')})
        else:
             return JSONResponse(status_code=404, content={"status": "error", "message": f"MO details not found for {mo_number}"})
    except Exception as e:
        logger.error(f"Failed to fetch MO details: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to fetch MO details: {str(e)}"}
        )
# Mount static files directory for serving frontend assets
# This allows the API to serve static files (HTML, CSS, JS) for the frontend
app.mount(f"{basePath}/static", StaticFiles(directory="static"), name="static")
