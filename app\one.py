import pandas as pd
from db import update_postgres, call_db_json, config
import psycopg2
import asyncio
import logging
import math

# Configure logging with INFO level to capture important operational events
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def periodic_cleanup():
    while True:
        try:
            # Log the start of cleanup for debugging and monitoring purposes
            logger.info("Starting periodic cart cleanup...")
            
            # Execute the cleanup function
            result = check_and_clean_carts()
            
            # Log the completion and results
            logger.info(f"Cleanup completed: {result}")
        except Exception as e:
            # Log any errors that occur during cleanup, but don't stop the loop
            logger.error(f"Error in periodic cleanup: {str(e)}")
        
        # Sleep for 5 minutes before next cleanup cycle
        await asyncio.sleep(300)  # 300 seconds = 5 minutes

def add_to_cart(batch_id, total_length, item_description, total_quantity, mos, hks, mode):
    """
    Adds a new cart entry to the database.
    
    This function inserts a new row into the 'carts' table with the provided parameters.
    It represents adding items (typically rails or manufacturing orders) to a user's cart.
    """
    # SQL query to insert a new cart record with current timestamp
    query = '''
    INSERT INTO public.carts 
    (batch_id, total_length, item_description, total_quantity, mos, hks, mode, created_at)
    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
    RETURNING id
    '''
    
    # Parameters for the SQL query
    params = (
        batch_id,
        total_length,
        item_description,
        total_quantity,
        mos,
        hks,
        mode
    )
    
    try:
        # Execute the query and return the new cart ID
        return update_postgres(query, params)
    except Exception as e:
        # Re-raise with a more informative message
        raise RuntimeError(f"Failed to add to cart: {str(e)}") from e

def check_and_clean_carts():
    """
    Checks all carts in the database and removes those with invalid manufacturing orders.
    
    A cart is considered invalid if all of its associated manufacturing orders (MOs)
    have a status other than 10. Status 10 appears to represent an active/valid MO.
    
    This function helps keep the database clean by removing stale or invalid cart data.
    
    """
    try:
        # Get all records from carts table
        query_carts = '''
        SELECT id, mos FROM public.carts
        '''
        cart_records = call_db_json(query_carts)
        deleted_count = 0
        
        # Process each cart record
        for record in cart_records:
            # Split MOS string into individual MO numbers
            mo_list = record['mos'].split(',')
            all_invalid = True
            
            # Check each MO status
            for mo in mo_list:
                mo = mo.strip()  # Remove any whitespace
                
                # Query to get the status of this MO
                query_mo_status = '''
                SELECT order_status 
                FROM public.manufacturing_orders 
                WHERE order_number = %s
                '''
                
                # Execute the query with the MO number
                status_result = call_db_json(query_mo_status % ("'" + mo + "'"))
                
                # If any MO has status 10, mark as valid and stop checking other MOs in this cart
                if status_result and status_result[0]['order_status'] == 10:
                    all_invalid = False
                    break
            
            # If all MOs are invalid (not status 10), delete the cart record
            if all_invalid:
                try:
                    # SQL query to delete the cart record
                    delete_query = '''
                    DELETE FROM public.carts 
                    WHERE id = %s
                    '''
                    
                    # Establish a new connection for this delete operation
                    conn = None
                    params_config = config(db="postgres")
                    conn = psycopg2.connect(**params_config)
                    cur = conn.cursor()
                    
                    # Execute the delete query
                    cur.execute(delete_query, (record['id'],))
                    conn.commit()
                    deleted_count += 1
                finally:
                    # Ensure the connection is closed even if an error occurs
                    if conn:
                        conn.close()
        
        # Return a summary of the operation
        return {
            "message": "Cart cleanup completed successfully",
            "records_processed": len(cart_records),
            "records_deleted": deleted_count
        }
    except Exception as e:
        # Re-raise with a more informative message
        raise RuntimeError(f"Failed to clean cart data: {str(e)}") from e

def calculate_pitch_holes(length, G, pitch):
    """
    Calculate the number and positions of pitch holes for a rail.
    
    This function determines where holes should be positioned along a rail
    based on its length, G value (distance from end to first hole),
    and pitch (distance between holes).  
    """
    if G > length:
        return []  # No holes can be drilled
    
    num_holes = 1 + math.floor((length - G) / pitch)  # Number of holes based on updated formula
    hole_positions = [G + i * pitch for i in range(num_holes)]  # Calculate positions
    
    return hole_positions

# ==================== Rail Arrangement Functions ====================

def find_valid_cut_position(cut, rail_length, margin, current_rail, hole_positions):
    """Find a valid position for placing a cut on a rail."""
    best_position = None
    best_holes = []
    best_waste = float('inf')
    
    for start_pos in range(margin, int(rail_length - cut['length'] - margin + 1)):
        if check_cut_overlap(cut, start_pos, current_rail['cuts'], margin):
            continue
        
        cut_holes = find_holes_for_cut(cut, start_pos, hole_positions)
        if not cut_holes:
            continue
        
        if cut['product_g1'] > 0 and not check_g1_compliance(cut_holes[0], start_pos, cut['product_g1']):
            continue
        
        waste = calculate_cut_waste(start_pos, cut, current_rail)
        if waste < best_waste:
            best_waste = waste
            best_position = start_pos
            best_holes = cut_holes
    
    return best_position, best_holes

def check_cut_overlap(cut, start_pos, existing_cuts, margin):
    """Check if a cut at a given position overlaps with existing cuts."""
    for existing_cut in existing_cuts:
        existing_end = existing_cut['position'] + existing_cut['length']
        if not (start_pos >= existing_end + margin or 
                start_pos + cut['length'] + margin <= existing_cut['position']):
            return True
    return False

def find_holes_for_cut(cut, start_pos, hole_positions):
    """Find holes that would be on this cut at a specific position."""
    cut_holes = []
    for hole_pos in hole_positions:
        if start_pos <= hole_pos <= start_pos + cut['length']:
            cut_holes.append(hole_pos)
    return cut_holes

def check_g1_compliance(first_hole_pos, start_pos, required_g1):
    """Check if first hole is at correct G1 distance from cut edge."""
    first_hole_distance = first_hole_pos - start_pos
    return abs(first_hole_distance - required_g1) <= 1.0

def calculate_cut_waste(position, cut, current_rail):
    """Calculate waste when placing a cut at a position."""
    all_positions = []
    for existing_cut in current_rail['cuts']:
        all_positions.append(existing_cut['position'])
        all_positions.append(existing_cut['position'] + existing_cut['length'])
    
    all_positions.append(position)
    all_positions.append(position + cut['length'])
    
    min_pos = min(all_positions)
    max_pos = max(all_positions)
    
    total_length = sum(c['length'] for c in current_rail['cuts']) + cut['length']
    return (max_pos - min_pos) - total_length

def try_fit_small_cut(small_cut, current_rail, rail_length, margin, hole_positions):
    """Try to fit a small cut into gaps in the current rail."""
    best_pos = None
    best_small_holes = []
    min_gap_waste = float('inf')
    
    for start_pos in range(margin, int(rail_length - small_cut['length'] - margin + 1)):
        if check_cut_overlap(small_cut, start_pos, current_rail['cuts'], margin):
            continue
        
        cut_holes = find_holes_for_cut(small_cut, start_pos, hole_positions)
        if not cut_holes:
            continue
        
        if small_cut['product_g1'] > 0 and not check_g1_compliance(cut_holes[0], start_pos, small_cut['product_g1']):
            continue
        
        gap_waste = calculate_gap_waste(start_pos, small_cut, current_rail)
        if gap_waste < min_gap_waste:
            min_gap_waste = gap_waste
            best_pos = start_pos
            best_small_holes = cut_holes
    
    return best_pos, best_small_holes

def calculate_gap_waste(position, cut, current_rail):
    """Calculate waste when placing a cut in a gap."""
    gap_positions = []
    for c in current_rail['cuts']:
        gap_positions.append(c['position'])
        gap_positions.append(c['position'] + c['length'])
    
    gap_positions.append(position)
    gap_positions.append(position + cut['length'])
    
    min_gap_pos = min(gap_positions)
    max_gap_pos = max(gap_positions)
    
    total_length = sum(c['length'] for c in current_rail['cuts']) + cut['length']
    return (max_gap_pos - min_gap_pos) - total_length

def format_rail_details(rails, rail_length, hole_positions, hks_list):
    """Format rail arrangement results into a standard structure."""
    rail_details = []
    
    for rail_idx, rail in enumerate(rails):
        rail['cuts'].sort(key=lambda x: x['position'])
        
        positions = []
        mos = []
        assigned_hks = []
        hole_assignments = []
        g1_actuals = []
        g1_requirements = []
        
        for cut in rail['cuts']:
            positions.append(cut['position'])
            mos.append(cut['mo_number'])
            
            hk_idx = cut['original_index']
            if hk_idx < len(hks_list):
                assigned_hks.append(hks_list[hk_idx])
            else:
                assigned_hks.append("")
                
            hole_assignments.append(cut['assigned_holes'])
            g1_actuals.append(cut.get('g1_actual'))
            g1_requirements.append(cut.get('product_g1', 0))
        
        utilized_length, remaining, utilization_rate = calculate_rail_utilization(rail, rail_length)
        
        rail_details.append({
            "rail_number": rail_idx + 1,
            "utilized_length": utilized_length,
            "remaining_length": remaining,
            "positions_mm": positions,
            "mos": mos,
            "hks": assigned_hks,
            "utilization_rate": utilization_rate,
            "hole_positions": hole_positions,
            "cut_hole_assignments": hole_assignments,
            "g1_actuals": g1_actuals,
            "g1_requirements": g1_requirements,
            "g1_satisfied": all(abs(actual - req) < 0.5 if actual is not None and req > 0 else True 
                             for actual, req in zip(g1_actuals, g1_requirements))
        })
    
    return rail_details

def calculate_rail_utilization(rail, rail_length):
    """Calculate utilization metrics for a rail."""
    if rail['cuts']:
        min_pos = min(c['position'] for c in rail['cuts'])
        max_pos = max(c['position'] + c['length'] for c in rail['cuts'])
        utilized_length = max_pos - min_pos
    else:
        utilized_length = 0
        
    remaining = rail_length - utilized_length
    utilization_rate = round((utilized_length / rail_length) * 100, 2)
    
    return utilized_length, remaining, utilization_rate

def optimize_rail_arrangement_with_g1_priority(mo_details_list, rail_length, margin, G, pitch, hks_list, hole_positions=None):
    """
    Optimizes rail cutting with G1 compliance as the highest priority.
    This version ensures:
    1. Each cut has first hole exactly at product_g1 distance (highest priority)
    2. All cuts have exact lengths as required (second priority)
    3. Waste is minimized (third priority)
    
    Enhanced with better error reporting for diagnostics.
    """
    margin = int(margin)
    G = int(G)
    pitch = int(pitch)
    
    # Use provided hole positions if available, otherwise calculate them
    if hole_positions is None:
        hole_positions = calculate_pitch_holes(rail_length, G, pitch)
    
    # Prepare cuts with hole requirements
    all_cuts = []
    for mo in mo_details_list:
        order_qty = int(mo['order_quantity'])
        product_length = float(mo['product_length'])
        product_g1 = float(mo['product_g1']) if 'product_g1' in mo and mo['product_g1'] is not None else 0
        
        for i in range(order_qty):
            all_cuts.append({
                'length': product_length,
                'mo_number': mo['mo_number'],
                'original_index': len(all_cuts),
                'product_g1': product_g1
            })
    
    # Sort cuts by length descending to place larger cuts first
    all_cuts.sort(key=lambda x: -x['length'])
    
    # Initialize rails
    rails = []
    
    # Track remaining cuts
    remaining_cuts = all_cuts.copy()
    
    # Track problematic cuts for detailed error reporting
    problematic_cuts = []
    
    while remaining_cuts:
        # Start a new rail
        current_rail = {
            'cuts': [], 
            'remaining_space': rail_length,
            'used_positions': set()
        }
        rails.append(current_rail)
        
        # Try to fill this rail
        i = 0
        while i < len(remaining_cuts):
            cut = remaining_cuts[i]
            
            # Find valid placement for this cut
            best_position, best_holes = find_valid_cut_position(cut, rail_length, margin, current_rail, hole_positions)
            
            # If we found a valid position, place the cut
            if best_position is not None:
                cut['position'] = best_position
                cut['assigned_holes'] = best_holes
                cut['g1_actual'] = best_holes[0] - best_position if best_holes else None
                
                current_rail['cuts'].append(cut)
                
                # Mark these holes as used
                for hole in best_holes:
                    current_rail['used_positions'].add(hole)
                
                # Remove from remaining cuts
                remaining_cuts.pop(i)
                
                # Try to fit smaller pieces into gaps (improved in next section)
                placed_small_cut = try_fit_small_cuts_improved(remaining_cuts, current_rail, rail_length, margin, hole_positions)
                if placed_small_cut:
                    # Since the list of remaining cuts has changed, reset the index
                    i = 0
                else:
                    # No small cut was placed, continue with the next cut
                    i = 0
            else:
                # No valid position for this cut, try the next one
                i += 1
        
        # Handle cases where no cuts could be placed
        if not current_rail['cuts'] and remaining_cuts:
            problem_cut = remaining_cuts.pop(0)
            # Collect detailed information about why this cut is problematic
            problem_details = {
                'mo_number': problem_cut['mo_number'],
                'length': problem_cut['length'],
                'g1_requirement': problem_cut['product_g1'],
                'issues': []
            }
            
            # Check for possible issues
            if problem_cut['length'] > rail_length - 2 * margin:
                problem_details['issues'].append(f"Cut length ({problem_cut['length']}) exceeds available rail length minus margins ({rail_length - 2 * margin})")
            
            # Check if G1 requirement can be satisfied with any hole
            g1_possible = False
            for hole_pos in hole_positions:
                if margin <= hole_pos - problem_cut['product_g1'] <= rail_length - problem_cut['length'] - margin:
                    g1_possible = True
                    break
            
            if not g1_possible and problem_cut['product_g1'] > 0:
                problem_details['issues'].append(f"G1 requirement ({problem_cut['product_g1']}) cannot be satisfied with available hole positions")
            
            if not problem_details['issues']:
                problem_details['issues'].append("Unknown placement issue - possibly conflicts with other cuts or no valid holes available")
            
            problematic_cuts.append(problem_details)
            logger.warning(f"Skipping problematic cut: {problem_details}")
    
    # If we had problematic cuts, log a summary
    if problematic_cuts:
        logger.warning(f"Completed with {len(problematic_cuts)} problematic cuts that could not be placed")
        for problem in problematic_cuts:
            logger.warning(f"MO {problem['mo_number']}: {problem['issues']}")
    
    # Format results
    result = format_rail_details_with_lengths(rails, rail_length, hole_positions, hks_list)
    
    # Add problematic cuts information to the result
    if problematic_cuts:
        for rail_detail in result:
            rail_detail['problematic_cuts'] = problematic_cuts
    
    return result

def try_fit_small_cuts_improved(remaining_cuts, current_rail, rail_length, margin, hole_positions):
    """
    Improved algorithm to fit small cuts by considering multiple possible arrangements.
    Attempts to place cuts in the most efficient way to minimize waste.
    
    Returns True if any cut was placed, False otherwise.
    """
    # If no cuts in the rail yet, nothing to optimize
    if not current_rail['cuts']:
        return False
    
    # Sort remaining cuts by length (smallest first for gap filling)
    temp_remaining = sorted(remaining_cuts, key=lambda x: x['length'])
    
    # Find all gaps in the current rail
    gaps = []
    
    # Add gap at the beginning if there's space
    min_pos = min(c['position'] for c in current_rail['cuts'])
    if min_pos > margin:
        gaps.append({
            'start': margin,
            'end': min_pos - margin,
            'length': min_pos - 2 * margin
        })
    
    # Add gap at the end if there's space
    max_end = max(c['position'] + c['length'] for c in current_rail['cuts'])
    if max_end < rail_length - margin:
        gaps.append({
            'start': max_end + margin,
            'end': rail_length - margin,
            'length': rail_length - max_end - 2 * margin
        })
    
    # Add gaps between cuts
    sorted_cuts = sorted(current_rail['cuts'], key=lambda x: x['position'])
    for i in range(len(sorted_cuts) - 1):
        end_of_current = sorted_cuts[i]['position'] + sorted_cuts[i]['length']
        start_of_next = sorted_cuts[i + 1]['position']
        
        gap_length = start_of_next - end_of_current - 2 * margin
        if gap_length > 0:
            gaps.append({
                'start': end_of_current + margin,
                'end': start_of_next - margin,
                'length': gap_length
            })
    
    # Sort gaps by length (largest first to prioritize filling big gaps)
    gaps.sort(key=lambda x: -x['length'])
    
    # Try to place cuts in gaps, starting with the best fit
    for gap in gaps:
        # Find cuts that could fit in this gap
        fitting_cuts = [cut for cut in temp_remaining if cut['length'] <= gap['length']]
        
        if not fitting_cuts:
            continue
        
        # First try: Find the largest cut that fits perfectly
        best_fit = None
        min_waste = float('inf')
        
        for cut in fitting_cuts:
            waste = gap['length'] - cut['length']
            if waste < min_waste:
                # Check if the cut can satisfy G1 requirements with available holes
                for start_pos in range(int(gap['start']), int(gap['end'] - cut['length'] + 1)):
                    cut_holes = find_holes_for_cut(cut, start_pos, hole_positions)
                    
                    if not cut_holes:
                        continue
                    
                    if cut['product_g1'] > 0 and not check_g1_compliance(cut_holes[0], start_pos, cut['product_g1']):
                        continue
                    
                    # This is a valid position
                    min_waste = waste
                    best_fit = {
                        'cut': cut,
                        'position': start_pos,
                        'holes': cut_holes
                    }
                    break
        
        if best_fit:
            # Place this cut
            cut_index = remaining_cuts.index(best_fit['cut'])
            best_fit['cut']['position'] = best_fit['position']
            best_fit['cut']['assigned_holes'] = best_fit['holes']
            best_fit['cut']['g1_actual'] = best_fit['holes'][0] - best_fit['position'] if best_fit['holes'] else None
            
            current_rail['cuts'].append(best_fit['cut'])
            
            for hole in best_fit['holes']:
                current_rail['used_positions'].add(hole)
            
            remaining_cuts.pop(cut_index)
            return True
    
    # Second attempt: Try combinations of two cuts to fill larger gaps efficiently
    for gap in gaps:
        if gap['length'] < 10:  # Too small for multiple cuts
            continue
            
        # Get cuts that could potentially fit
        potential_cuts = [cut for cut in temp_remaining if cut['length'] <= gap['length']]
        
        if len(potential_cuts) < 2:
            continue
            
        best_combo = None
        min_waste = float('inf')
        
        # Try all combinations of two cuts
        for i in range(len(potential_cuts)):
            for j in range(i + 1, len(potential_cuts)):
                cut1 = potential_cuts[i]
                cut2 = potential_cuts[j]
                
                combined_length = cut1['length'] + cut2['length'] + margin
                if combined_length > gap['length']:
                    continue
                    
                waste = gap['length'] - combined_length
                if waste < min_waste:
                    # Check if first cut can be placed with valid G1
                    valid_pos1 = None
                    valid_holes1 = None
                    
                    for pos1 in range(int(gap['start']), int(gap['end'] - combined_length + 1)):
                        holes1 = find_holes_for_cut(cut1, pos1, hole_positions)
                        if not holes1:
                            continue
                        
                        if cut1['product_g1'] > 0 and not check_g1_compliance(holes1[0], pos1, cut1['product_g1']):
                            continue
                            
                        valid_pos1 = pos1
                        valid_holes1 = holes1
                        break
                        
                    if valid_pos1 is None:
                        continue
                        
                    # Check if second cut can be placed with valid G1
                    pos2 = valid_pos1 + cut1['length'] + margin
                    holes2 = find_holes_for_cut(cut2, pos2, hole_positions)
                    
                    if not holes2:
                        continue
                        
                    if cut2['product_g1'] > 0 and not check_g1_compliance(holes2[0], pos2, cut2['product_g1']):
                        continue
                        
                    # Found valid combination
                    min_waste = waste
                    best_combo = {
                        'cut1': {'cut': cut1, 'position': valid_pos1, 'holes': valid_holes1},
                        'cut2': {'cut': cut2, 'position': pos2, 'holes': holes2}
                    }
                    
        if best_combo:
            # Place first cut
            cut1_index = remaining_cuts.index(best_combo['cut1']['cut'])
            best_combo['cut1']['cut']['position'] = best_combo['cut1']['position']
            best_combo['cut1']['cut']['assigned_holes'] = best_combo['cut1']['holes']
            best_combo['cut1']['cut']['g1_actual'] = (
                best_combo['cut1']['holes'][0] - best_combo['cut1']['position'] 
                if best_combo['cut1']['holes'] else None
            )
            
            current_rail['cuts'].append(best_combo['cut1']['cut'])
            
            for hole in best_combo['cut1']['holes']:
                current_rail['used_positions'].add(hole)
                
            # Remove from remaining cuts (have to do this first before index changes)
            remaining_cuts.pop(cut1_index)
            
            # Place second cut
            cut2_index = remaining_cuts.index(best_combo['cut2']['cut'])
            best_combo['cut2']['cut']['position'] = best_combo['cut2']['position'] 
            best_combo['cut2']['cut']['assigned_holes'] = best_combo['cut2']['holes']
            best_combo['cut2']['cut']['g1_actual'] = (
                best_combo['cut2']['holes'][0] - best_combo['cut2']['position']
                if best_combo['cut2']['holes'] else None
            )
            
            current_rail['cuts'].append(best_combo['cut2']['cut'])
            
            for hole in best_combo['cut2']['holes']:
                current_rail['used_positions'].add(hole)
                
            remaining_cuts.pop(cut2_index)
            return True
            
    return False


def format_rail_details_with_lengths(rails, rail_length, hole_positions, hks_list):
    """
    Format rail arrangement results into a standard structure with actual cut lengths included.
    """
    rail_details = []
    
    for rail_idx, rail in enumerate(rails):
        rail['cuts'].sort(key=lambda x: x['position'])
        
        positions = []
        cut_lengths = []  # Add this new array for actual lengths
        mos = []
        assigned_hks = []
        hole_assignments = []
        g1_actuals = []
        g1_requirements = []
        
        for cut in rail['cuts']:
            positions.append(cut['position'])
            cut_lengths.append(cut['length'])  # Store the actual length
            mos.append(cut['mo_number'])
            
            hk_idx = cut['original_index']
            if hk_idx < len(hks_list):
                assigned_hks.append(hks_list[hk_idx])
            else:
                assigned_hks.append("")
                
            hole_assignments.append(cut['assigned_holes'])
            g1_actuals.append(cut.get('g1_actual'))
            g1_requirements.append(cut.get('product_g1', 0))
        
        utilized_length, remaining, utilization_rate = calculate_rail_utilization(rail, rail_length)
        
        rail_details.append({
            "rail_number": rail_idx + 1,
            "rail_total_length": rail_length,  # Add total rail length for frontend
            "utilized_length": utilized_length,
            "remaining_length": remaining,
            "positions_mm": positions,
            "cut_lengths": cut_lengths,  # Include actual cut lengths
            "mos": mos,
            "hks": assigned_hks,
            "utilization_rate": utilization_rate,
            "hole_positions": hole_positions,
            "cut_hole_assignments": hole_assignments,
            "g1_actuals": g1_actuals,
            "g1_requirements": g1_requirements,
            "g1_satisfied": all(abs(actual - req) < 0.5 if actual is not None and req > 0 else True 
                             for actual, req in zip(g1_actuals, g1_requirements))
        })
    
    return rail_details

def select_optimal_rail(material_specs, rail_type):
    """Select the best rail for a given rail type."""
    suitable_rails = [
        (name, spec) for name, spec in material_specs.items() 
        if spec['type'] == rail_type
    ]
    
    if not suitable_rails:
        logger.warning(f"No suitable rails found for type {rail_type}")
        return None, None
        
    # Select optimal rail (longest available)
    selected_rail_name, selected_rail = max(suitable_rails, key=lambda x: x[1]['length'])
    return selected_rail_name, selected_rail


def create_auto_arrangement(specific_mos=None, specific_hks=None):
    """
    Creates an optimized arrangement of rail cuts based on manufacturing orders.
    Groups by rail type only, ignoring cart/batch concepts.
    """
    try:
        # Material specifications database (unchanged)
        material_specs = get_material_specs_from_db()

        logger.info("Starting auto arrangement data gathering...")

        # Collect all MOs from both carts and manual input
        all_mos = []
        all_hks = []
        
        # Get MOs from carts table
        query = '''
        SELECT mos, hks FROM public.carts WHERE mode = 'auto'
        '''
        cart_data = call_db_json(query)
        
        # Extract MOs from carts
        for cart in cart_data:
            mos_list = [mo.strip() for mo in cart['mos'].split(',')]
            hks_list = [hk.strip() for hk in cart['hks'].split(',')]
            all_mos.extend(mos_list)
            all_hks.extend(hks_list)
        
        # Add specific MOs from user input
        if specific_mos:
            for mo in specific_mos:
                if mo not in all_mos:
                    all_mos.append(mo)
                    all_hks.append("")  # Empty HK for manually added MOs
        
        # Remove any empty strings and duplicates while preserving order
        all_mos = [mo for mo in all_mos if mo]
        unique_mos = []
        unique_hks = []
        seen_mos = set()
        
        for i, mo in enumerate(all_mos):
            if mo not in seen_mos:
                unique_mos.append(mo)
                unique_hks.append(all_hks[i] if i < len(all_hks) else "")
                seen_mos.add(mo)
        
        # Group MOs by rail type
        rail_type_groups = {}
        
        for i, mo in enumerate(unique_mos):
            details = get_mo_details(mo)
            
            if details:
                # Extract rail type from item description
                rail_type = details['rail_type']
                
                if rail_type not in rail_type_groups:
                    rail_type_groups[rail_type] = {
                        'mo_details': [],
                        'hks': []
                    }
                
                details['mo_number'] = mo
                rail_type_groups[rail_type]['mo_details'].append(details)
                rail_type_groups[rail_type]['hks'].append(unique_hks[i])
        
        # Process each rail type group
        processing_results = []
        
        for rail_type, group_data in rail_type_groups.items():
            logger.info(f"Processing rail type: {rail_type} with {len(group_data['mo_details'])} MOs")
            
            # Find suitable rails for this type
            suitable_rails = [
                (name, spec) for name, spec in material_specs.items() 
                if spec['type'] == rail_type
            ]
            
            if not suitable_rails:
                logger.warning(f"No suitable rails found for type {rail_type}")
                continue
                
            # Select optimal rail (longest available)
            selected_rail_name, selected_rail = max(suitable_rails, key=lambda x: x[1]['length'])
            rail_length = float(selected_rail['length'])
            margin = float(selected_rail['margin'])
            G = float(selected_rail['G'])
            pitch = float(selected_rail['pitch'])

            # Calculate hole positions using corrected method
            hole_positions = calculate_pitch_holes(rail_length, G, pitch)
            
            # Calculate total requirements for this rail type
            total_required_length = 0
            total_quantity = 0
            
            for mo_detail in group_data['mo_details']:
                total_required_length += mo_detail['product_length'] * mo_detail['order_quantity']
                total_quantity += mo_detail['order_quantity']

            # Run optimization with G1 priority
            rail_details = optimize_rail_arrangement_with_g1_priority(
                mo_details_list=group_data['mo_details'],
                rail_length=rail_length,
                margin=margin,
                G=G,
                pitch=pitch,
                hks_list=group_data['hks'],
                hole_positions=hole_positions
            )
            
            # Format result for this rail type
            result = {
                "rail_type": rail_type,
                "material_spec_used": selected_rail_name,
                "total_required_length": total_required_length,
                "total_quantity": total_quantity,
                "rail_details": rail_details
            }
            
            processing_results.append(result)
        
        return {
            "status": "success",
            "results": processing_results
        }
        
    except Exception as e:
        logger.error(f"Auto arrangement failed: {str(e)}")
        raise Exception(f"Auto arrangement processing failed: {str(e)}")


def get_mo_details(mo_number: str):
    """
    Retrieve manufacturing order details from database.
    Sets rail_type by combining product_family and product_model.
    """
    query = f'''
    SELECT item_description, product_length, product_g1, product_pitch, order_quantity,product_family, product_model 
    FROM public.manufacturing_orders 
    WHERE order_number = '{mo_number}'
    '''
    try:
        result = call_db_json(query)
        if result and len(result) > 0:
            # Make sure numeric values are proper types
            record = result[0]
            
            # Convert string values to appropriate numeric types
            record['product_length'] = float(record['product_length'])
            record['product_g1'] = float(record['product_g1']) if record['product_g1'] is not None else 0
            record['product_pitch'] = float(record['product_pitch']) if record['product_pitch'] is not None else 0
            record['order_quantity'] = int(record['order_quantity'])
            
            # Determine rail type by combining product_family and product_model
            product_family = record.get('product_family', '')
            # print(product_family)
            product_model = record.get('product_model', '')
            # print(product_model)
            # record['rail_type']=str(record['product_family'])+str(record['product_model'])
            
            # Simple concatenation of family and model
            if product_family and product_model:
                record['rail_type']=product_family+product_model
            else:
                # Fallback to item_description if product_family not available
                record['rail_type'] = record.get('item_description', '').split('-')[0]
            
            return record
        return None
    except Exception as e:
        raise RuntimeError(f"Failed to fetch MO details: {str(e)}") from e
def get_material_specs_from_db():
    """
    Fetches material specifications from the material_specs database table
    and formats them into a dictionary compatible with the optimization algorithm.
    
    Returns:
        dict: Material specifications keyed by rail_desc with properties as values
    """
    try:
        # Query to select all material specs from the table
        query = '''
        SELECT rail_desc, length, pitch, g, margin, cut_time, type
        FROM public.material_specs
        '''
        
        # Execute the query to get all material specs
        results = call_db_json(query)
        
        if not results:
            logger.warning("No material specifications found in database. Using default values.")
            # Return a minimal default set to prevent crashes
            return {
                "DEFAULT": {"length": 9000, "pitch": 60, "G": 30, "margin": 7, "cut_time": 40, "type": "DEFAULT"}
            }
        
        # Convert results to dictionary format matching the current structure
        material_specs = {}
        for row in results:
            material_specs[row['rail_desc']] = {
                "length": float(row['length']),
                "pitch": float(row['pitch']),
                "G": float(row['g']),
                "margin": float(row['margin']),
                "cut_time": float(row['cut_time']),
                "type": row['type']
            }
        
        logger.info(f"Successfully loaded {len(material_specs)} material specifications from database")
        return material_specs
        
    except Exception as e:
        logger.error(f"Failed to fetch material specifications from database: {str(e)}")
        # In case of error, return a minimal default set to prevent crashes
        return {
            "DEFAULT": {"length": 9000, "pitch": 60, "G": 30, "margin": 7, "cut_time": 40, "type": "DEFAULT"}
        }